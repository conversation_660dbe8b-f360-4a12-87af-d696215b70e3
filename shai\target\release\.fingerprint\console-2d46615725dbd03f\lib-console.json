{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi-parsing\", \"default\", \"std\", \"unicode-width\"]", "declared_features": "[\"alloc\", \"ansi-parsing\", \"default\", \"std\", \"unicode-width\", \"windows-console-colors\"]", "target": 7600203407108534355, "profile": 2040997289075261528, "path": 9660464691978407776, "deps": [[3050609750276885141, "encode_unicode", false, 9934279231034018394], [3722963349756955755, "once_cell", false, 9505186314257102092], [5330658427305787935, "libc", false, 9115342085629088884], [6389928905734779823, "unicode_width", false, 4944152829597845871], [7263319592666514104, "windows_sys", false, 8814607188416440615]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\console-2d46615725dbd03f\\dep-lib-console", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}