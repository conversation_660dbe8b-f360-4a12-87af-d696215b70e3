{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/providers/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,qCAA0C;AA+HxC,+FA/HO,uBAAc,OA+HP;AA9HhB,mDAAmD;AACnD,qCAA0C;AA+HxC,+FA/HO,uBAAc,OA+HP;AA9HhB,yCAA8C;AA+H5C,iGA/HO,2BAAgB,OA+HP;AA9HlB,+CAA+C;AAC/C,yCAA8C;AA+H5C,iGA/HO,2BAAgB,OA+HP;AA5HlB,MAAa,eAAe;IAG1B;QAFQ,cAAS,GAAkC,IAAI,GAAG,EAAE,CAAC;QAG3D,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEO,mBAAmB;QACzB,gEAAgE;QAChE,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,uBAAc,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;QAC/E,CAAC;QAED,0EAA0E;QAC1E,uCAAuC;QACvC,2FAA2F;QAC3F,IAAI;QAEJ,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAC/B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,uBAAc,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;QAC/E,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC;YACjC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,2BAAgB,CACjD,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAC5B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAC9B,CAAC,CAAC;QACL,CAAC;QAED,wEAAwE;QACxE,qCAAqC;QACrC,qFAAqF;QACrF,IAAI;QAEJ,yDAAyD;QACzD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,2BAAgB,CACjD,OAAO,CAAC,GAAG,CAAC,WAAW,EACvB,OAAO,CAAC,GAAG,CAAC,YAAY,CACzB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,CAAC,SAAS,CAAC,IAAI,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAClG,CAAC;IAED,WAAW,CAAC,IAAkB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,eAAe;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED,qBAAqB;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,MAAM,aAAa,GAAmB,EAAE,CAAC;QAEzC,KAAK,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAC/C,MAAM,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,MAAM,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAE3D,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI;oBACJ,YAAY,EAAE,QAAQ,CAAC,WAAW;oBAClC,MAAM;oBACN,SAAS;iBACV,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,aAAa,CAAC,IAAI,CAAC;oBACjB,IAAI;oBACJ,YAAY,EAAE,QAAQ,CAAC,WAAW;oBAClC,MAAM,EAAE,EAAE;oBACV,SAAS,EAAE,KAAK;oBAChB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;iBAC9D,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;IACpF,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAC/C,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;QAE1F,OAAO;YACL,SAAS;YACT,YAAY;SACb,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAe;QAC7B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC/C,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,SAAS,EAAE,CAAC;gBAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;gBACjD,IAAI,KAAK,EAAE,CAAC;oBACV,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,QAAQ,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;YACtE,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,IAAkB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IAC7B,CAAC;CACF;AAnHD,0CAmHC;AAYD,eAAe;AACf,2CAAyB"}