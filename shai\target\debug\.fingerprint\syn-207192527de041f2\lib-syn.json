{"rustc": 1842507548689473721, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 12410652206962508598, "path": 17618353857677137023, "deps": [[1988483478007900009, "unicode_ident", false, 11985597343903133893], [3060637413840920116, "proc_macro2", false, 10218515410445853022], [17990358020177143287, "quote", false, 3856419848312089831]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-207192527de041f2\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}