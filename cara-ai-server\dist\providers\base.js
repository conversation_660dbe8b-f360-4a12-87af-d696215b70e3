"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseProvider = void 0;
class BaseProvider {
    constructor(apiKey, baseUrl) {
        this.apiKey = apiKey;
        this.baseUrl = baseUrl;
    }
    validateRequest(request) {
        if (!request.messages || request.messages.length === 0) {
            throw new Error('Messages array cannot be empty');
        }
        if (!request.model) {
            throw new Error('Model must be specified');
        }
        // Validate message format
        for (const message of request.messages) {
            if (!message.role || !['system', 'user', 'assistant'].includes(message.role)) {
                throw new Error(`Invalid message role: ${message.role}`);
            }
            if (!message.content) {
                throw new Error('Message content cannot be empty');
            }
        }
    }
    createStreamChunk(content, id, finish_reason) {
        return {
            type: 'chunk',
            id,
            content,
            finish_reason
        };
    }
    createErrorChunk(error) {
        return {
            type: 'error',
            error
        };
    }
    createDoneChunk(id) {
        return {
            type: 'done',
            id
        };
    }
    generateId() {
        return `chatcmpl-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    getCurrentTimestamp() {
        return Math.floor(Date.now() / 1000);
    }
    async handleProviderError(error, context) {
        console.error(`${this.name} provider error in ${context}:`, error);
        let message = `${this.displayName} error: `;
        if (error.response?.data?.error?.message) {
            message += error.response.data.error.message;
        }
        else if (error.message) {
            message += error.message;
        }
        else {
            message += 'Unknown error occurred';
        }
        throw new Error(message);
    }
}
exports.BaseProvider = BaseProvider;
//# sourceMappingURL=base.js.map