import { OpenAIProvider } from './openai';
import { GeminiProvider } from './gemini';
import { DeepSeekProvider } from './deepseek';
import { OVHCloudProvider } from './ovhcloud';
import { AIProvider, ProviderName, ProviderInfo, ModelInfo } from '../types';
export declare class ProviderManager {
    private providers;
    constructor();
    private initializeProviders;
    getProvider(name: ProviderName): AIProvider | undefined;
    getAllProviders(): AIProvider[];
    getAvailableProviders(): ProviderName[];
    getProviderInfo(): Promise<ProviderInfo[]>;
    getAllModels(): Promise<{
        providers: ProviderInfo[];
        total_models: number;
    }>;
    findModel(modelId: string): Promise<{
        provider: AIProvider;
        model: ModelInfo;
    } | null>;
    hasProvider(name: ProviderName): boolean;
    getProviderCount(): number;
}
export { OpenAIProvider, GeminiProvider, DeepSeekProvider, OVHCloudProvider };
export * from '../types';
//# sourceMappingURL=index.d.ts.map