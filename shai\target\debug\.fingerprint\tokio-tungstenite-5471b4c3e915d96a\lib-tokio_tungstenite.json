{"rustc": 1842507548689473721, "features": "[\"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"stream\", \"tokio-native-tls\"]", "declared_features": "[\"__rustls-tls\", \"connect\", \"default\", \"handshake\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "target": 2433367608443825, "profile": 2241668132362809309, "path": 10906727695908055555, "deps": [[5986029879202738730, "log", false, 3586916075725266993], [8258418851280347661, "tungstenite", false, 11378959523883577652], [9538054652646069845, "tokio", false, 5432783451998911839], [10629569228670356391, "futures_util", false, 4810424942971930661], [12186126227181294540, "tokio_native_tls", false, 7757535702436556777], [16785601910559813697, "native_tls_crate", false, 11058285236891797874]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tokio-tungstenite-5471b4c3e915d96a\\dep-lib-tokio_tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}