import { BaseProvider } from './base';
import { ChatRequest, ChatResponse, StreamChunk, ModelInfo, ProviderName } from '../types';
export declare class DeepSeekProvider extends BaseProvider {
    name: ProviderName;
    displayName: string;
    private client;
    constructor(apiKey: string, baseUrl?: string);
    isAvailable(): Promise<boolean>;
    getModels(): Promise<ModelInfo[]>;
    chat(request: ChatRequest): Promise<ChatResponse>;
    chatStream(request: ChatRequest): AsyncGenerator<StreamChunk, void, unknown>;
}
//# sourceMappingURL=deepseek.d.ts.map