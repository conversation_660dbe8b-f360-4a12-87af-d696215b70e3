{"rustc": 1842507548689473721, "features": "[\"default\", \"std\"]", "declared_features": "[\"bindgen\", \"default\", \"std\", \"wasm\", \"wasmtime-c-api\"]", "target": 625726317299402132, "profile": 9033685258343967486, "path": 14092775650810111595, "deps": [[1882578318385763075, "build_script_build", false, 9551761436025860129], [6343042414849818538, "tree_sitter_language", false, 15993766897018791144], [9235208004366183979, "streaming_iterator", false, 14665105250176540508], [9408802513701742484, "regex_syntax", false, 12461707605168070718], [9451456094439810778, "regex", false, 17849562051594462071]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tree-sitter-37a150b8907de32d\\dep-lib-tree_sitter", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}