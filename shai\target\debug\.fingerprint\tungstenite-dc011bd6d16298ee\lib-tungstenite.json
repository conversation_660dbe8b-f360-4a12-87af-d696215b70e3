{"rustc": 1842507548689473721, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 1270341572213479472, "profile": 2241668132362809309, "path": 1864323656967012350, "deps": [[99287295355353247, "data_encoding", false, 14033808193516246807], [3150220818285335163, "url", false, 13627468668894949648], [3712811570531045576, "byteorder", false, 17899748120891940673], [4359956005902820838, "utf8", false, 16683714755489487417], [5986029879202738730, "log", false, 3586916075725266993], [6163892036024256188, "httparse", false, 13183580438389388776], [8008191657135824715, "thiserror", false, 13734932076053129473], [9010263965687315507, "http", false, 14254829711276313855], [10724389056617919257, "sha1", false, 15132377466840526887], [13208667028893622512, "rand", false, 3190218141877043823], [16066129441945555748, "bytes", false, 14053255451738189556], [16785601910559813697, "native_tls_crate", false, 11058285236891797874]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tungstenite-dc011bd6d16298ee\\dep-lib-tungstenite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}