"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MistralProvider = void 0;
const mistralai_1 = __importDefault(require("@mistralai/mistralai"));
const base_1 = require("./base");
class MistralProvider extends base_1.BaseProvider {
    constructor(apiKey) {
        super(apiKey);
        this.name = 'mistral';
        this.displayName = 'Mistral AI';
        this.client = new mistralai_1.default({
            apiKey: this.apiKey
        });
    }
    async isAvailable() {
        try {
            await this.client.models.list();
            return true;
        }
        catch (error) {
            console.error('Mistral availability check failed:', error);
            return false;
        }
    }
    async getModels() {
        try {
            const response = await this.client.models.list();
            return response.data.map((model) => ({
                id: model.id,
                name: model.id,
                provider: this.name,
                description: `Mistral ${model.id}`,
                supports_functions: model.id.includes('large') || model.id.includes('medium'),
                supports_streaming: true
            })).sort((a, b) => {
                // Prioritize larger models
                if (a.id.includes('large') && !b.id.includes('large'))
                    return -1;
                if (!a.id.includes('large') && b.id.includes('large'))
                    return 1;
                return a.id.localeCompare(b.id);
            });
        }
        catch (error) {
            // If models endpoint fails, return known models
            return [
                {
                    id: 'mistral-large-latest',
                    name: 'Mistral Large',
                    provider: this.name,
                    description: 'Most capable Mistral model',
                    context_length: 32000,
                    supports_functions: true,
                    supports_streaming: true
                },
                {
                    id: 'mistral-medium-latest',
                    name: 'Mistral Medium',
                    provider: this.name,
                    description: 'Balanced Mistral model',
                    context_length: 32000,
                    supports_functions: true,
                    supports_streaming: true
                },
                {
                    id: 'mistral-small-latest',
                    name: 'Mistral Small',
                    provider: this.name,
                    description: 'Fast and efficient Mistral model',
                    context_length: 32000,
                    supports_functions: false,
                    supports_streaming: true
                }
            ];
        }
    }
    async chat(request) {
        this.validateRequest(request);
        try {
            const response = await this.client.chat.complete({
                model: request.model,
                messages: request.messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                })),
                temperature: request.temperature,
                maxTokens: request.max_tokens,
                topP: request.top_p,
                stream: false
            });
            const id = this.generateId();
            return {
                id,
                object: 'chat.completion',
                created: this.getCurrentTimestamp(),
                model: request.model,
                choices: [{
                        index: 0,
                        message: {
                            role: 'assistant',
                            content: response.choices[0].message.content || ''
                        },
                        finish_reason: response.choices[0].finishReason || 'stop'
                    }],
                usage: response.usage ? {
                    prompt_tokens: response.usage.promptTokens,
                    completion_tokens: response.usage.completionTokens,
                    total_tokens: response.usage.totalTokens
                } : undefined
            };
        }
        catch (error) {
            throw await this.handleProviderError(error, 'chat');
        }
    }
    async *chatStream(request) {
        this.validateRequest(request);
        try {
            const stream = await this.client.chat.stream({
                model: request.model,
                messages: request.messages.map(msg => ({
                    role: msg.role,
                    content: msg.content
                })),
                temperature: request.temperature,
                maxTokens: request.max_tokens,
                topP: request.top_p
            });
            const id = this.generateId();
            for await (const chunk of stream) {
                if (chunk.data.choices[0]?.delta?.content) {
                    yield this.createStreamChunk(chunk.data.choices[0].delta.content, id, chunk.data.choices[0].finishReason);
                }
                if (chunk.data.choices[0]?.finishReason) {
                    yield this.createDoneChunk(id);
                    break;
                }
            }
        }
        catch (error) {
            yield this.createErrorChunk(`Mistral streaming error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
exports.MistralProvider = MistralProvider;
//# sourceMappingURL=mistral.js.map