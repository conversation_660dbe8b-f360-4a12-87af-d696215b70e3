import { BaseProvider } from './base';
import { ChatRequest, ChatResponse, StreamChunk, ModelInfo, ProviderName } from '../types';
export declare class GeminiProvider extends BaseProvider {
    name: ProviderName;
    displayName: string;
    private client;
    constructor(apiKey: string);
    isAvailable(): Promise<boolean>;
    getModels(): Promise<ModelInfo[]>;
    private convertMessages;
    chat(request: ChatRequest): Promise<ChatResponse>;
    chatStream(request: ChatRequest): AsyncGenerator<StreamChunk, void, unknown>;
}
//# sourceMappingURL=gemini.d.ts.map