{"rustc": 1842507548689473721, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"multipart\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 2153345239813930883, "deps": [[40386456601120721, "percent_encoding", false, 10074128938820299051], [418947936956741439, "h2", false, 15623723370976026777], [778154619793643451, "hyper_util", false, 15188958841823511385], [784494742817713399, "tower_service", false, 16143800549481638644], [1288403060204016458, "tokio_util", false, 4412729669456572016], [1906322745568073236, "pin_project_lite", false, 14460408057010957084], [2054153378684941554, "tower_http", false, 10411317011549444256], [2517136641825875337, "sync_wrapper", false, 11870354669814581653], [2883436298747778685, "rustls_pki_types", false, 3030677646891825156], [3150220818285335163, "url", false, 15809581770919317096], [5695049318159433696, "tower", false, 272879549477045080], [5986029879202738730, "log", false, 17975795364312500675], [7620660491849607393, "futures_core", false, 1040340781229989006], [9010263965687315507, "http", false, 5091234905551711815], [9538054652646069845, "tokio", false, 14698379664695426373], [9689903380558560274, "serde", false, 746175189569263742], [10229185211513642314, "mime", false, 13586842324850158678], [10629569228670356391, "futures_util", false, 8452443711613238102], [11957360342995674422, "hyper", false, 6454091486766914710], [12186126227181294540, "tokio_native_tls", false, 17557132380228120848], [13077212702700853852, "base64", false, 6348174203703308072], [14084095096285906100, "http_body", false, 2948466287527954173], [14564311161534545801, "encoding_rs", false, 15217393083287779385], [15367738274754116744, "serde_json", false, 4917936035312756425], [16066129441945555748, "bytes", false, 16022464871568311542], [16542808166767769916, "serde_urlencoded", false, 8149587876467951386], [16785601910559813697, "native_tls_crate", false, 6677678048894660181], [16900715236047033623, "http_body_util", false, 9422588233004010938], [18071510856783138481, "mime_guess", false, 7038361606189793688], [18273243456331255970, "hyper_tls", false, 3532383725796408259]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-3dcf902ffa07c5e6\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}