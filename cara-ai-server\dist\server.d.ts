export declare class CaraAIServer {
    private app;
    private server;
    private wss;
    private providerManager;
    private port;
    private startTime;
    constructor(port?: number);
    private setupMiddleware;
    private setupRoutes;
    private setupWebSocket;
    private handleWebSocketMessage;
    private handleChatRequest;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=server.d.ts.map