import Mistral from '@mistralai/mistralai';
import { BaseProvider } from './base';
import { ChatRequest, ChatResponse, StreamChunk, ModelInfo, ProviderName } from '../types';

export class MistralProvider extends BaseProvider {
  name: ProviderName = 'mistral';
  displayName = 'Mistral AI';
  private client: Mistral;

  constructor(apiKey: string) {
    super(apiKey);
    this.client = new Mistral({
      apiKey: this.apiKey!
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      console.error('Mistral availability check failed:', error);
      return false;
    }
  }

  async getModels(): Promise<ModelInfo[]> {
    try {
      const response = await this.client.models.list();
      return response.data.map((model: any) => ({
        id: model.id,
        name: model.id,
        provider: this.name,
        description: `Mistral ${model.id}`,
        supports_functions: model.id.includes('large') || model.id.includes('medium'),
        supports_streaming: true
      })).sort((a: any, b: any) => {
        // Prioritize larger models
        if (a.id.includes('large') && !b.id.includes('large')) return -1;
        if (!a.id.includes('large') && b.id.includes('large')) return 1;
        return a.id.localeCompare(b.id);
      });
    } catch (error) {
      // If models endpoint fails, return known models
      return [
        {
          id: 'mistral-large-latest',
          name: 'Mistral Large',
          provider: this.name,
          description: 'Most capable Mistral model',
          context_length: 32000,
          supports_functions: true,
          supports_streaming: true
        },
        {
          id: 'mistral-medium-latest',
          name: 'Mistral Medium',
          provider: this.name,
          description: 'Balanced Mistral model',
          context_length: 32000,
          supports_functions: true,
          supports_streaming: true
        },
        {
          id: 'mistral-small-latest',
          name: 'Mistral Small',
          provider: this.name,
          description: 'Fast and efficient Mistral model',
          context_length: 32000,
          supports_functions: false,
          supports_streaming: true
        }
      ];
    }
  }

  async chat(request: ChatRequest): Promise<ChatResponse> {
    this.validateRequest(request);
    
    try {
      const response = await this.client.chat.complete({
        model: request.model,
        messages: request.messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature: request.temperature,
        maxTokens: request.max_tokens,
        topP: request.top_p,
        stream: false
      });

      const id = this.generateId();
      return {
        id,
        object: 'chat.completion',
        created: this.getCurrentTimestamp(),
        model: request.model,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response.choices[0].message.content || ''
          },
          finish_reason: response.choices[0].finishReason || 'stop'
        }],
        usage: response.usage ? {
          prompt_tokens: response.usage.promptTokens,
          completion_tokens: response.usage.completionTokens,
          total_tokens: response.usage.totalTokens
        } : undefined
      };
    } catch (error) {
      throw await this.handleProviderError(error, 'chat');
    }
  }

  async *chatStream(request: ChatRequest): AsyncGenerator<StreamChunk, void, unknown> {
    this.validateRequest(request);
    
    try {
      const stream = await this.client.chat.stream({
        model: request.model,
        messages: request.messages.map(msg => ({
          role: msg.role,
          content: msg.content
        })),
        temperature: request.temperature,
        maxTokens: request.max_tokens,
        topP: request.top_p
      });

      const id = this.generateId();

      for await (const chunk of stream) {
        if (chunk.data.choices[0]?.delta?.content) {
          yield this.createStreamChunk(
            chunk.data.choices[0].delta.content,
            id,
            chunk.data.choices[0].finishReason
          );
        }
        
        if (chunk.data.choices[0]?.finishReason) {
          yield this.createDoneChunk(id);
          break;
        }
      }
    } catch (error) {
      yield this.createErrorChunk(`Mistral streaming error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
