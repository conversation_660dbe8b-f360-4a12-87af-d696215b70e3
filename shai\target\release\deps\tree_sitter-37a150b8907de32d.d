D:\Sandeep\AXR\shai\target\release\deps\tree_sitter-37a150b8907de32d.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\../src/parser.h D:\Sandeep\AXR\shai\target\release\build\tree-sitter-1bb84f931f809677\out/stdlib-symbols.txt

D:\Sandeep\AXR\shai\target\release\deps\libtree_sitter-37a150b8907de32d.rlib: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\../src/parser.h D:\Sandeep\AXR\shai\target\release\build\tree-sitter-1bb84f931f809677\out/stdlib-symbols.txt

D:\Sandeep\AXR\shai\target\release\deps\libtree_sitter-37a150b8907de32d.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\../src/parser.h D:\Sandeep\AXR\shai\target\release\build\tree-sitter-1bb84f931f809677\out/stdlib-symbols.txt

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\ffi.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\util.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./README.md:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./bindings.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\../src/parser.h:
D:\Sandeep\AXR\shai\target\release\build\tree-sitter-1bb84f931f809677\out/stdlib-symbols.txt:

# env-dep:OUT_DIR=D:\\Sandeep\\AXR\\shai\\target\\release\\build\\tree-sitter-1bb84f931f809677\\out
