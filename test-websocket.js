const WebSocket = require('ws');

console.log('Testing WebSocket connection to Cara AI Server...');

const ws = new WebSocket('ws://localhost:3001');

ws.on('open', function open() {
  console.log('✅ WebSocket connected successfully!');
  
  // Test sending a simple message
  const testMessage = {
    type: 'chat',
    provider: 'ovhcloud',
    model: 'Qwen3-32B',
    messages: [
      {
        role: 'user',
        content: 'Hello! Please respond with just "Hello from Cara AI!"'
      }
    ]
  };
  
  console.log('Sending test message...');
  ws.send(JSON.stringify(testMessage));
});

ws.on('message', function message(data) {
  console.log('📨 Received:', data.toString());
});

ws.on('error', function error(err) {
  console.error('❌ WebSocket error:', err.message);
});

ws.on('close', function close() {
  console.log('🔌 WebSocket connection closed');
  process.exit(0);
});

// Close after 10 seconds
setTimeout(() => {
  console.log('⏰ Closing connection...');
  ws.close();
}, 10000);
