"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepSeekProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const base_1 = require("./base");
class DeepSeekProvider extends base_1.BaseProvider {
    constructor(apiKey, baseUrl = 'https://api.deepseek.com/v1') {
        super(apiKey, baseUrl);
        this.name = 'deepseek';
        this.displayName = 'DeepSeek';
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json'
            }
        });
    }
    async isAvailable() {
        try {
            await this.client.get('/models');
            return true;
        }
        catch (error) {
            console.error('DeepSeek availability check failed:', error);
            return false;
        }
    }
    async getModels() {
        try {
            const response = await this.client.get('/models');
            return response.data.data
                .filter((model) => model.id.includes('deepseek'))
                .map((model) => ({
                id: model.id,
                name: model.id,
                provider: this.name,
                description: `DeepSeek ${model.id}`,
                supports_functions: true,
                supports_streaming: true
            }))
                .sort((a, b) => a.id.localeCompare(b.id));
        }
        catch (error) {
            // If models endpoint fails, return known models
            return [
                {
                    id: 'deepseek-chat',
                    name: 'DeepSeek Chat',
                    provider: this.name,
                    description: 'DeepSeek conversational model',
                    context_length: 32000,
                    supports_functions: true,
                    supports_streaming: true
                },
                {
                    id: 'deepseek-coder',
                    name: 'DeepSeek Coder',
                    provider: this.name,
                    description: 'DeepSeek specialized coding model',
                    context_length: 16000,
                    supports_functions: true,
                    supports_streaming: true
                }
            ];
        }
    }
    async chat(request) {
        this.validateRequest(request);
        try {
            const response = await this.client.post('/chat/completions', {
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.max_tokens,
                top_p: request.top_p,
                frequency_penalty: request.frequency_penalty,
                presence_penalty: request.presence_penalty,
                stop: request.stop,
                stream: false
            });
            return response.data;
        }
        catch (error) {
            throw await this.handleProviderError(error, 'chat');
        }
    }
    async *chatStream(request) {
        this.validateRequest(request);
        try {
            const response = await this.client.post('/chat/completions', {
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.max_tokens,
                top_p: request.top_p,
                frequency_penalty: request.frequency_penalty,
                presence_penalty: request.presence_penalty,
                stop: request.stop,
                stream: true
            }, {
                responseType: 'stream'
            });
            let buffer = '';
            for await (const chunk of response.data) {
                buffer += chunk.toString();
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6).trim();
                        if (data === '[DONE]') {
                            yield this.createDoneChunk();
                            return;
                        }
                        try {
                            const parsed = JSON.parse(data);
                            const choice = parsed.choices?.[0];
                            if (choice?.delta?.content) {
                                yield this.createStreamChunk(choice.delta.content, parsed.id, choice.finish_reason);
                            }
                            if (choice?.finish_reason) {
                                yield this.createDoneChunk(parsed.id);
                                return;
                            }
                        }
                        catch (parseError) {
                            // Skip invalid JSON lines
                            continue;
                        }
                    }
                }
            }
        }
        catch (error) {
            yield this.createErrorChunk(`DeepSeek streaming error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
exports.DeepSeekProvider = DeepSeekProvider;
//# sourceMappingURL=deepseek.js.map