import Anthropic from '@anthropic-ai/sdk';
import { BaseProvider } from './base';
import { ChatRequest, ChatResponse, StreamChunk, ModelInfo, ProviderName, ChatMessage } from '../types';

export class AnthropicProvider extends BaseProvider {
  name: ProviderName = 'anthropic';
  displayName = 'Anthropic';
  private client: Anthropic;

  constructor(apiKey: string) {
    super(apiKey);
    this.client = new Anthropic({
      apiKey: this.apiKey!
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      // Anthropic doesn't have a models endpoint, so we'll try a simple request
      await this.client.messages.create({
        model: 'claude-3-haiku-20240307',
        max_tokens: 1,
        messages: [{ role: 'user', content: 'Hi' }]
      });
      return true;
    } catch (error) {
      console.error('Anthropic availability check failed:', error);
      return false;
    }
  }

  async getModels(): Promise<ModelInfo[]> {
    // Anthropic doesn't provide a models API, so we'll return known models
    return [
      {
        id: 'claude-3-5-sonnet-20241022',
        name: 'Claude 3.5 Sonnet',
        provider: this.name,
        description: 'Most intelligent model, best for complex tasks',
        context_length: 200000,
        supports_functions: true,
        supports_streaming: true
      },
      {
        id: 'claude-3-opus-20240229',
        name: 'Claude 3 Opus',
        provider: this.name,
        description: 'Powerful model for highly complex tasks',
        context_length: 200000,
        supports_functions: true,
        supports_streaming: true
      },
      {
        id: 'claude-3-sonnet-20240229',
        name: 'Claude 3 Sonnet',
        provider: this.name,
        description: 'Balance of intelligence and speed',
        context_length: 200000,
        supports_functions: true,
        supports_streaming: true
      },
      {
        id: 'claude-3-haiku-20240307',
        name: 'Claude 3 Haiku',
        provider: this.name,
        description: 'Fastest model, good for simple tasks',
        context_length: 200000,
        supports_functions: true,
        supports_streaming: true
      }
    ];
  }

  private convertMessages(messages: ChatMessage[]): { system?: string; messages: MessageParam[] } {
    const systemMessage = messages.find(m => m.role === 'system');
    const conversationMessages = messages.filter(m => m.role !== 'system');

    return {
      system: systemMessage?.content,
      messages: conversationMessages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      }))
    };
  }

  async chat(request: ChatRequest): Promise<ChatResponse> {
    this.validateRequest(request);
    
    try {
      const { system, messages } = this.convertMessages(request.messages);
      
      const response = await this.client.messages.create({
        model: request.model,
        max_tokens: request.max_tokens || 2048,
        temperature: request.temperature,
        top_p: request.top_p,
        system,
        messages,
        stream: false
      });

      const id = this.generateId();
      return {
        id,
        object: 'chat.completion',
        created: this.getCurrentTimestamp(),
        model: request.model,
        choices: [{
          index: 0,
          message: {
            role: 'assistant',
            content: response.content[0].type === 'text' ? response.content[0].text : ''
          },
          finish_reason: response.stop_reason || 'stop'
        }],
        usage: {
          prompt_tokens: response.usage.input_tokens,
          completion_tokens: response.usage.output_tokens,
          total_tokens: response.usage.input_tokens + response.usage.output_tokens
        }
      };
    } catch (error) {
      throw await this.handleProviderError(error, 'chat');
    }
  }

  async *chatStream(request: ChatRequest): AsyncGenerator<StreamChunk, void, unknown> {
    this.validateRequest(request);
    
    try {
      const { system, messages } = this.convertMessages(request.messages);
      const id = this.generateId();
      
      const stream = await this.client.messages.create({
        model: request.model,
        max_tokens: request.max_tokens || 2048,
        temperature: request.temperature,
        top_p: request.top_p,
        system,
        messages,
        stream: true
      });

      for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta' && chunk.delta.type === 'text_delta') {
          yield this.createStreamChunk(chunk.delta.text, id);
        } else if (chunk.type === 'message_stop') {
          yield this.createDoneChunk(id);
          break;
        }
      }
    } catch (error) {
      yield this.createErrorChunk(`Anthropic streaming error: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
