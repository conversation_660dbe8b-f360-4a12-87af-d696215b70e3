{"rustc": 1842507548689473721, "features": "[\"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"multipart\", \"stream\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 447021486742529345, "path": 2153345239813930883, "deps": [[40386456601120721, "percent_encoding", false, 17461702652862544679], [418947936956741439, "h2", false, 233889316652034240], [778154619793643451, "hyper_util", false, 13781345127405253532], [784494742817713399, "tower_service", false, 8409841863435742618], [1288403060204016458, "tokio_util", false, 754596817518635642], [1906322745568073236, "pin_project_lite", false, 12041619693214720961], [2054153378684941554, "tower_http", false, 12385490631807427611], [2517136641825875337, "sync_wrapper", false, 4423327889294911902], [2883436298747778685, "rustls_pki_types", false, 3833055109872856463], [3150220818285335163, "url", false, 13627468668894949648], [5695049318159433696, "tower", false, 12051816234312005421], [5986029879202738730, "log", false, 3586916075725266993], [7620660491849607393, "futures_core", false, 4048761348304805076], [9010263965687315507, "http", false, 14254829711276313855], [9538054652646069845, "tokio", false, 5432783451998911839], [9689903380558560274, "serde", false, 3174016326301431393], [10229185211513642314, "mime", false, 5827565216931984928], [10629569228670356391, "futures_util", false, 4810424942971930661], [11957360342995674422, "hyper", false, 3853645490373688680], [12186126227181294540, "tokio_native_tls", false, 7757535702436556777], [13077212702700853852, "base64", false, 3972029268817490431], [14084095096285906100, "http_body", false, 18428748453503590490], [14564311161534545801, "encoding_rs", false, 16469145117729943815], [15367738274754116744, "serde_json", false, 10148534288263972318], [16066129441945555748, "bytes", false, 14053255451738189556], [16542808166767769916, "serde_urlencoded", false, 3999897531105742611], [16785601910559813697, "native_tls_crate", false, 11058285236891797874], [16900715236047033623, "http_body_util", false, 1748204918033655440], [18071510856783138481, "mime_guess", false, 16483710694786539287], [18273243456331255970, "hyper_tls", false, 3172119267872240729]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-efefc02b86a8713a\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}