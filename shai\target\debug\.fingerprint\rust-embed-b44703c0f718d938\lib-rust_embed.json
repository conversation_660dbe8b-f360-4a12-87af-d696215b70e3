{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"actix\", \"actix-web\", \"axum\", \"axum-ex\", \"compression\", \"debug-embed\", \"deterministic-timestamps\", \"hex\", \"include-exclude\", \"include-flate\", \"interpolate-folder-path\", \"mime-guess\", \"mime_guess\", \"poem\", \"poem-ex\", \"rocket\", \"salvo\", \"salvo-ex\", \"tokio\", \"warp\", \"warp-ex\"]", "target": 3385222681681722461, "profile": 2241668132362809309, "path": 13381408126050167754, "deps": [[5409933923103361951, "rust_embed_utils", false, 2322524793958174370], [11693977163544003021, "rust_embed_impl", false, 18262054994855662341], [15622660310229662834, "walkdir", false, 13066052315635845172]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rust-embed-b44703c0f718d938\\dep-lib-rust_embed", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}