{"rustc": 1842507548689473721, "features": "[\"default\", \"regex\"]", "declared_features": "[\"default\", \"lite\", \"perf\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"regex\", \"regex-lite\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 373025063261422169, "profile": 2241668132362809309, "path": 7791883380217912071, "deps": [[3722963349756955755, "once_cell", false, 10205856087722752526], [9451456094439810778, "regex", false, 15629433151217737311], [15278928094109957391, "lazy_regex_proc_macros", false, 4750161595661137941]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\lazy-regex-de5e7d29988d62e4\\dep-lib-lazy_regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}