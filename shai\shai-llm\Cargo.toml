[package]
name = "shai-llm"
version = "0.1.0"
edition = "2021"

[dependencies]
# New LLM module dependencies
async-trait = "0.1"
reqwest = { version = "0.12", features = ["json", "stream"] }
futures = "0.3"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }
tokio = { version = "1.0", features = ["full"] }
openai_dive = { version = "1.2", features = ["stream"] }
async-stream = "0.3"
reqwest-eventsource = "0.6"
regex = "1.0"
schemars = "1.0.1"
shai-macros = { path = "../shai-macros" }
fastrand = "2.0"
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
paste = "1.0"

[lints.rust]
dead_code = "allow"
unused_variables = "allow"
unused_mut = "allow"
unused_imports = "allow"

[[example]]
name = "basic_query"
path = "src/examples/basic_query.rs"

[[example]]
name = "query_with_history"
path = "src/examples/query_with_history.rs"

[[example]]
name = "streaming_query"
path = "src/examples/streaming_query.rs"

[[example]]
name = "function_calling"
path = "src/examples/function_calling.rs"

[[example]]
name = "function_calling_streaming"
path = "src/examples/function_calling_streaming.rs"

