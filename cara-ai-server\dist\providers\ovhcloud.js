"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OVHCloudProvider = void 0;
const axios_1 = __importDefault(require("axios"));
const base_1 = require("./base");
class OVHCloudProvider extends base_1.BaseProvider {
    constructor(apiKey, baseUrl = 'https://qwen-3-32b.endpoints.kepler.ai.cloud.ovh.net/api/openai_compat/v1') {
        super(apiKey, baseUrl);
        this.name = 'ovhcloud';
        this.displayName = 'OVH Cloud';
        this.client = axios_1.default.create({
            baseURL: this.baseUrl,
            headers: {
                'Content-Type': 'application/json',
                ...(this.apiKey && { 'Authorization': `Bearer ${this.apiKey}` })
            }
        });
    }
    async isAvailable() {
        try {
            // Try to get models or make a simple request
            const response = await this.client.get('/models');
            return response.status === 200;
        }
        catch (error) {
            // OVH Cloud might work without API key in anonymous mode
            console.warn('OVH Cloud availability check failed, but may work in anonymous mode:', error instanceof Error ? error.message : String(error));
            return true; // Allow it to be available for anonymous usage
        }
    }
    async getModels() {
        try {
            const response = await this.client.get('/models');
            if (response.data && response.data.data) {
                return response.data.data.map((model) => ({
                    id: model.id,
                    name: model.id,
                    provider: this.name,
                    description: `OVH Cloud ${model.id}`,
                    supports_functions: true,
                    supports_streaming: true
                }));
            }
        }
        catch (error) {
            console.warn('Failed to get models from OVH Cloud, using default:', error instanceof Error ? error.message : String(error));
        }
        // Return default model if API call fails
        return [
            {
                id: 'Qwen3-32B',
                name: 'Qwen 3 32B',
                provider: this.name,
                description: 'OVH Cloud Qwen 3 32B model (anonymous access)',
                context_length: 32000,
                supports_functions: true,
                supports_streaming: true
            }
        ];
    }
    async chat(request) {
        this.validateRequest(request);
        try {
            const response = await this.client.post('/chat/completions', {
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.max_tokens,
                top_p: request.top_p,
                frequency_penalty: request.frequency_penalty,
                presence_penalty: request.presence_penalty,
                stop: request.stop,
                stream: false
            });
            return response.data;
        }
        catch (error) {
            throw await this.handleProviderError(error, 'chat');
        }
    }
    async *chatStream(request) {
        this.validateRequest(request);
        try {
            const response = await this.client.post('/chat/completions', {
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.max_tokens,
                top_p: request.top_p,
                frequency_penalty: request.frequency_penalty,
                presence_penalty: request.presence_penalty,
                stop: request.stop,
                stream: true
            }, {
                responseType: 'stream'
            });
            let buffer = '';
            for await (const chunk of response.data) {
                buffer += chunk.toString();
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';
                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6).trim();
                        if (data === '[DONE]') {
                            yield this.createDoneChunk();
                            return;
                        }
                        try {
                            const parsed = JSON.parse(data);
                            const choice = parsed.choices?.[0];
                            if (choice?.delta?.content) {
                                yield this.createStreamChunk(choice.delta.content, parsed.id, choice.finish_reason);
                            }
                            if (choice?.finish_reason) {
                                yield this.createDoneChunk(parsed.id);
                                return;
                            }
                        }
                        catch (parseError) {
                            // Skip invalid JSON lines
                            continue;
                        }
                    }
                }
            }
        }
        catch (error) {
            yield this.createErrorChunk(`OVH Cloud streaming error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
exports.OVHCloudProvider = OVHCloudProvider;
//# sourceMappingURL=ovhcloud.js.map