import { AIProvider, ChatRequest, ChatResponse, StreamChunk, ModelInfo, ProviderName } from '../types';
export declare abstract class BaseProvider implements AIProvider {
    abstract name: ProviderName;
    abstract displayName: string;
    protected apiKey?: string;
    protected baseUrl?: string;
    constructor(apiKey?: string, baseUrl?: string);
    abstract isAvailable(): Promise<boolean>;
    abstract getModels(): Promise<ModelInfo[]>;
    abstract chat(request: ChatRequest): Promise<ChatResponse>;
    abstract chatStream(request: ChatRequest): AsyncGenerator<StreamChunk, void, unknown>;
    protected validateRequest(request: ChatRequest): void;
    protected createStreamChunk(content: string, id?: string, finish_reason?: string | null): StreamChunk;
    protected createErrorChunk(error: string): StreamChunk;
    protected createDoneChunk(id?: string): StreamChunk;
    protected generateId(): string;
    protected getCurrentTimestamp(): number;
    protected handleProviderError(error: any, context: string): Promise<never>;
}
//# sourceMappingURL=base.d.ts.map