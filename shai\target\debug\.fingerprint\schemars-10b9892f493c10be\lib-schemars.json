{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"schemars_derive\", \"std\"]", "declared_features": "[\"_ui_test\", \"arrayvec07\", \"bigdecimal04\", \"bytes1\", \"chrono04\", \"default\", \"derive\", \"either1\", \"indexmap2\", \"jiff02\", \"preserve_order\", \"raw_value\", \"rust_decimal1\", \"schemars_derive\", \"semver1\", \"smallvec1\", \"smol_str02\", \"std\", \"url2\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2241668132362809309, "path": 11749559102293743368, "deps": [[9122563107207267705, "dyn_clone", false, 4155065841996060038], [9689903380558560274, "serde", false, 3174016326301431393], [13535282280064720520, "ref_cast", false, 4055944512424321483], [14624475729786384435, "schemars_derive", false, 7961239494981108213], [15367738274754116744, "serde_json", false, 10148534288263972318]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-10b9892f493c10be\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}