{"version": 3, "file": "base.d.ts", "sourceRoot": "", "sources": ["../../src/providers/base.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,UAAU,CAAC;AAEvG,8BAAsB,YAAa,YAAW,UAAU;IACtD,QAAQ,CAAC,IAAI,EAAE,YAAY,CAAC;IAC5B,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;IAE7B,SAAS,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IAC1B,SAAS,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;gBAEf,MAAM,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM;IAK7C,QAAQ,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;IACxC,QAAQ,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;IAC1C,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAC1D,QAAQ,CAAC,UAAU,CAAC,OAAO,EAAE,WAAW,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,CAAC;IAErF,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,WAAW,GAAG,IAAI;IAoBrD,SAAS,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,WAAW;IASrG,SAAS,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW;IAOtD,SAAS,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,WAAW;IAOnD,SAAS,CAAC,UAAU,IAAI,MAAM;IAI9B,SAAS,CAAC,mBAAmB,IAAI,MAAM;cAIvB,mBAAmB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC;CAejF"}