{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 11202463608144111571, "path": 10812796613434319891, "deps": [[1009387600818341822, "matchers", false, 7569384996402327334], [1017461770342116999, "sharded_slab", false, 16233357318608990526], [1359731229228270592, "thread_local", false, 4776414734510325323], [3424551429995674438, "tracing_core", false, 1042625210038437221], [3666196340704888985, "smallvec", false, 15262314953216221536], [3722963349756955755, "once_cell", false, 10205856087722752526], [6981130804689348050, "tracing_serde", false, 5552060928298718833], [8606274917505247608, "tracing", false, 4082791803751026407], [8614575489689151157, "nu_ansi_term", false, 8961076012898877174], [9451456094439810778, "regex", false, 15629433151217737311], [9689903380558560274, "serde", false, 3174016326301431393], [10806489435541507125, "tracing_log", false, 11662935721232562714], [15367738274754116744, "serde_json", false, 10148534288263972318]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-3b9757bd1d38719b\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}