const express = require('express');
const app = express();
const port = 3001;

app.get('/health', (req, res) => {
  console.log('Health check requested');
  res.json({ 
    status: 'healthy', 
    version: '1.0.0',
    uptime: Date.now(),
    providers: {
      ovhcloud: { available: true },
      gemini: { available: true }
    }
  });
});

app.listen(port, () => {
  console.log(`Test server running on port ${port}`);
  console.log(`Health check: http://localhost:${port}/health`);
});

process.on('SIGINT', () => {
  console.log('Server shutting down...');
  process.exit(0);
});
