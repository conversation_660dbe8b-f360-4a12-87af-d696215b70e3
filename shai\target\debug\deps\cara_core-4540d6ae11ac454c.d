D:\Sandeep\AXR\shai\target\debug\deps\cara_core-4540d6ae11ac454c.d: cara-core\src\lib.rs cara-core\src\tools\mod.rs cara-core\src\tools\types.rs cara-core\src\tools\highlight.rs cara-core\src\tools\todo\mod.rs cara-core\src\tools\todo\structs.rs cara-core\src\tools\todo\todo.rs cara-core\src\tools\fs\mod.rs cara-core\src\tools\fs\edit\mod.rs cara-core\src\tools\fs\edit\structs.rs cara-core\src\tools\fs\edit\edit.rs cara-core\src\tools\fs\find\mod.rs cara-core\src\tools\fs\find\structs.rs cara-core\src\tools\fs\find\find.rs cara-core\src\tools\fs\ls\mod.rs cara-core\src\tools\fs\ls\structs.rs cara-core\src\tools\fs\ls\ls.rs cara-core\src\tools\fs\multiedit\mod.rs cara-core\src\tools\fs\multiedit\structs.rs cara-core\src\tools\fs\multiedit\multiedit.rs cara-core\src\tools\fs\operation_log.rs cara-core\src\tools\fs\read\mod.rs cara-core\src\tools\fs\read\structs.rs cara-core\src\tools\fs\read\read.rs cara-core\src\tools\fs\write\mod.rs cara-core\src\tools\fs\write\structs.rs cara-core\src\tools\fs\write\write.rs cara-core\src\tools\fetch\mod.rs cara-core\src\tools\fetch\structs.rs cara-core\src\tools\fetch\fetch.rs cara-core\src\tools\bash\mod.rs cara-core\src\tools\bash\structs.rs cara-core\src\tools\bash\bash.rs cara-core\src\agent\mod.rs cara-core\src\agent\builder.rs cara-core\src\agent\claims.rs cara-core\src\agent\error.rs cara-core\src\agent\brain.rs cara-core\src\agent\agent.rs cara-core\src\agent\protocol.rs cara-core\src\agent\events.rs cara-core\src\agent\states\mod.rs cara-core\src\agent\states\states.rs cara-core\src\agent\states\pause.rs cara-core\src\agent\states\running.rs cara-core\src\agent\states\starting.rs cara-core\src\agent\states\processing.rs cara-core\src\agent\states\terminal.rs cara-core\src\agent\actions\mod.rs cara-core\src\agent\actions\brain.rs cara-core\src\agent\actions\tools.rs cara-core\src\agent\output\mod.rs cara-core\src\agent\output\stdout.rs cara-core\src\agent\output\pretty.rs cara-core\src\agent\output\log.rs cara-core\src\runners\mod.rs cara-core\src\runners\coder\mod.rs cara-core\src\runners\coder\coder.rs cara-core\src\runners\coder\prompt.rs cara-core\src\runners\coder\env.rs cara-core\src\runners\compacter\mod.rs cara-core\src\runners\compacter\compact.rs cara-core\src\runners\gerund\mod.rs cara-core\src\runners\gerund\prompt.rs cara-core\src\runners\gerund\gerund.rs cara-core\src\runners\clifixer\mod.rs cara-core\src\runners\clifixer\prompt.rs cara-core\src\runners\clifixer\fix.rs cara-core\src\logging.rs cara-core\src\config\mod.rs cara-core\src\config\config.rs

D:\Sandeep\AXR\shai\target\debug\deps\libcara_core-4540d6ae11ac454c.rmeta: cara-core\src\lib.rs cara-core\src\tools\mod.rs cara-core\src\tools\types.rs cara-core\src\tools\highlight.rs cara-core\src\tools\todo\mod.rs cara-core\src\tools\todo\structs.rs cara-core\src\tools\todo\todo.rs cara-core\src\tools\fs\mod.rs cara-core\src\tools\fs\edit\mod.rs cara-core\src\tools\fs\edit\structs.rs cara-core\src\tools\fs\edit\edit.rs cara-core\src\tools\fs\find\mod.rs cara-core\src\tools\fs\find\structs.rs cara-core\src\tools\fs\find\find.rs cara-core\src\tools\fs\ls\mod.rs cara-core\src\tools\fs\ls\structs.rs cara-core\src\tools\fs\ls\ls.rs cara-core\src\tools\fs\multiedit\mod.rs cara-core\src\tools\fs\multiedit\structs.rs cara-core\src\tools\fs\multiedit\multiedit.rs cara-core\src\tools\fs\operation_log.rs cara-core\src\tools\fs\read\mod.rs cara-core\src\tools\fs\read\structs.rs cara-core\src\tools\fs\read\read.rs cara-core\src\tools\fs\write\mod.rs cara-core\src\tools\fs\write\structs.rs cara-core\src\tools\fs\write\write.rs cara-core\src\tools\fetch\mod.rs cara-core\src\tools\fetch\structs.rs cara-core\src\tools\fetch\fetch.rs cara-core\src\tools\bash\mod.rs cara-core\src\tools\bash\structs.rs cara-core\src\tools\bash\bash.rs cara-core\src\agent\mod.rs cara-core\src\agent\builder.rs cara-core\src\agent\claims.rs cara-core\src\agent\error.rs cara-core\src\agent\brain.rs cara-core\src\agent\agent.rs cara-core\src\agent\protocol.rs cara-core\src\agent\events.rs cara-core\src\agent\states\mod.rs cara-core\src\agent\states\states.rs cara-core\src\agent\states\pause.rs cara-core\src\agent\states\running.rs cara-core\src\agent\states\starting.rs cara-core\src\agent\states\processing.rs cara-core\src\agent\states\terminal.rs cara-core\src\agent\actions\mod.rs cara-core\src\agent\actions\brain.rs cara-core\src\agent\actions\tools.rs cara-core\src\agent\output\mod.rs cara-core\src\agent\output\stdout.rs cara-core\src\agent\output\pretty.rs cara-core\src\agent\output\log.rs cara-core\src\runners\mod.rs cara-core\src\runners\coder\mod.rs cara-core\src\runners\coder\coder.rs cara-core\src\runners\coder\prompt.rs cara-core\src\runners\coder\env.rs cara-core\src\runners\compacter\mod.rs cara-core\src\runners\compacter\compact.rs cara-core\src\runners\gerund\mod.rs cara-core\src\runners\gerund\prompt.rs cara-core\src\runners\gerund\gerund.rs cara-core\src\runners\clifixer\mod.rs cara-core\src\runners\clifixer\prompt.rs cara-core\src\runners\clifixer\fix.rs cara-core\src\logging.rs cara-core\src\config\mod.rs cara-core\src\config\config.rs

cara-core\src\lib.rs:
cara-core\src\tools\mod.rs:
cara-core\src\tools\types.rs:
cara-core\src\tools\highlight.rs:
cara-core\src\tools\todo\mod.rs:
cara-core\src\tools\todo\structs.rs:
cara-core\src\tools\todo\todo.rs:
cara-core\src\tools\fs\mod.rs:
cara-core\src\tools\fs\edit\mod.rs:
cara-core\src\tools\fs\edit\structs.rs:
cara-core\src\tools\fs\edit\edit.rs:
cara-core\src\tools\fs\find\mod.rs:
cara-core\src\tools\fs\find\structs.rs:
cara-core\src\tools\fs\find\find.rs:
cara-core\src\tools\fs\ls\mod.rs:
cara-core\src\tools\fs\ls\structs.rs:
cara-core\src\tools\fs\ls\ls.rs:
cara-core\src\tools\fs\multiedit\mod.rs:
cara-core\src\tools\fs\multiedit\structs.rs:
cara-core\src\tools\fs\multiedit\multiedit.rs:
cara-core\src\tools\fs\operation_log.rs:
cara-core\src\tools\fs\read\mod.rs:
cara-core\src\tools\fs\read\structs.rs:
cara-core\src\tools\fs\read\read.rs:
cara-core\src\tools\fs\write\mod.rs:
cara-core\src\tools\fs\write\structs.rs:
cara-core\src\tools\fs\write\write.rs:
cara-core\src\tools\fetch\mod.rs:
cara-core\src\tools\fetch\structs.rs:
cara-core\src\tools\fetch\fetch.rs:
cara-core\src\tools\bash\mod.rs:
cara-core\src\tools\bash\structs.rs:
cara-core\src\tools\bash\bash.rs:
cara-core\src\agent\mod.rs:
cara-core\src\agent\builder.rs:
cara-core\src\agent\claims.rs:
cara-core\src\agent\error.rs:
cara-core\src\agent\brain.rs:
cara-core\src\agent\agent.rs:
cara-core\src\agent\protocol.rs:
cara-core\src\agent\events.rs:
cara-core\src\agent\states\mod.rs:
cara-core\src\agent\states\states.rs:
cara-core\src\agent\states\pause.rs:
cara-core\src\agent\states\running.rs:
cara-core\src\agent\states\starting.rs:
cara-core\src\agent\states\processing.rs:
cara-core\src\agent\states\terminal.rs:
cara-core\src\agent\actions\mod.rs:
cara-core\src\agent\actions\brain.rs:
cara-core\src\agent\actions\tools.rs:
cara-core\src\agent\output\mod.rs:
cara-core\src\agent\output\stdout.rs:
cara-core\src\agent\output\pretty.rs:
cara-core\src\agent\output\log.rs:
cara-core\src\runners\mod.rs:
cara-core\src\runners\coder\mod.rs:
cara-core\src\runners\coder\coder.rs:
cara-core\src\runners\coder\prompt.rs:
cara-core\src\runners\coder\env.rs:
cara-core\src\runners\compacter\mod.rs:
cara-core\src\runners\compacter\compact.rs:
cara-core\src\runners\gerund\mod.rs:
cara-core\src\runners\gerund\prompt.rs:
cara-core\src\runners\gerund\gerund.rs:
cara-core\src\runners\clifixer\mod.rs:
cara-core\src\runners\clifixer\prompt.rs:
cara-core\src\runners\clifixer\fix.rs:
cara-core\src\logging.rs:
cara-core\src\config\mod.rs:
cara-core\src\config\config.rs:
