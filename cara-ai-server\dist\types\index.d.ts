export interface ChatMessage {
    role: 'system' | 'user' | 'assistant';
    content: string;
    name?: string;
}
export interface ChatRequest {
    provider: string;
    model: string;
    messages: ChatMessage[];
    stream?: boolean;
    temperature?: number;
    max_tokens?: number;
    top_p?: number;
    frequency_penalty?: number;
    presence_penalty?: number;
    stop?: string | string[];
    tools?: ToolDefinition[];
    tool_choice?: 'auto' | 'none' | {
        type: 'function';
        function: {
            name: string;
        };
    };
}
export interface ChatResponse {
    id: string;
    object: string;
    created: number;
    model: string;
    choices: ChatChoice[];
    usage?: TokenUsage;
}
export interface ChatChoice {
    index: number;
    message: ChatMessage;
    finish_reason: string | null;
}
export interface StreamChunk {
    type: 'chunk' | 'error' | 'done';
    id?: string;
    content?: string;
    error?: string;
    finish_reason?: string | null;
}
export interface TokenUsage {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
}
export interface ModelInfo {
    id: string;
    name: string;
    provider: string;
    description?: string;
    context_length?: number;
    supports_functions?: boolean;
    supports_streaming?: boolean;
}
export interface ProviderInfo {
    name: string;
    display_name: string;
    models: ModelInfo[];
    available: boolean;
    error?: string;
}
export interface ModelsResponse {
    providers: ProviderInfo[];
    total_models: number;
}
export interface HealthResponse {
    status: 'healthy' | 'unhealthy';
    version: string;
    uptime: number;
    providers: {
        [key: string]: {
            available: boolean;
            error?: string;
        };
    };
}
export interface WebSocketMessage {
    type: 'chat_request' | 'chat_chunk' | 'chat_error' | 'chat_done' | 'models_request' | 'models_response' | 'tool_call' | 'tool_result';
    id: string;
    data?: any;
}
export interface ProviderConfig {
    name: string;
    apiKey?: string;
    baseUrl?: string;
    enabled: boolean;
}
export type ProviderName = 'openai' | 'anthropic' | 'gemini' | 'deepseek' | 'mistral' | 'openrouter' | 'ovhcloud' | 'ollama';
export interface ToolDefinition {
    type: 'function';
    function: {
        name: string;
        description: string;
        parameters: any;
    };
}
export interface ToolCall {
    id: string;
    type: 'function';
    function: {
        name: string;
        arguments: string;
    };
}
export interface ToolResult {
    tool_call_id: string;
    output: string;
    error?: string;
}
export interface AIProvider {
    name: ProviderName;
    displayName: string;
    isAvailable(): Promise<boolean>;
    getModels(): Promise<ModelInfo[]>;
    chat(request: ChatRequest): Promise<ChatResponse>;
    chatStream(request: ChatRequest): AsyncGenerator<StreamChunk, void, unknown>;
}
//# sourceMappingURL=index.d.ts.map