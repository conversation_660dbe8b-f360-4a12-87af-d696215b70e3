"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CaraAIServer = void 0;
const express_1 = __importDefault(require("express"));
const http_1 = require("http");
const ws_1 = __importDefault(require("ws"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const providers_1 = require("./providers");
const uuid_1 = require("uuid");
class CaraAIServer {
    constructor(port = 3001) {
        this.port = port;
        this.startTime = Date.now();
        this.app = (0, express_1.default)();
        this.server = (0, http_1.createServer)(this.app);
        this.wss = new ws_1.default.Server({ server: this.server });
        this.providerManager = new providers_1.ProviderManager();
        this.setupMiddleware();
        this.setupRoutes();
        this.setupWebSocket();
    }
    setupMiddleware() {
        this.app.use((0, helmet_1.default)());
        this.app.use((0, cors_1.default)({
            origin: process.env.CORS_ORIGIN || '*',
            credentials: true
        }));
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true }));
        // Request logging
        this.app.use((req, res, next) => {
            console.log(`${new Date().toISOString()} ${req.method} ${req.path}`);
            next();
        });
    }
    setupRoutes() {
        // Health check
        this.app.get('/health', async (req, res) => {
            const providers = await this.providerManager.getProviderInfo();
            const providerStatus = {};
            providers.forEach(provider => {
                providerStatus[provider.name] = {
                    available: provider.available,
                    error: provider.error
                };
            });
            const health = {
                status: 'healthy',
                version: '1.0.0',
                uptime: Date.now() - this.startTime,
                providers: providerStatus
            };
            res.json(health);
        });
        // Get all models
        this.app.get('/models', async (req, res) => {
            try {
                const models = await this.providerManager.getAllModels();
                res.json(models);
            }
            catch (error) {
                console.error('Error getting models:', error);
                res.status(500).json({ error: 'Failed to get models' });
            }
        });
        // Chat completion (non-streaming)
        this.app.post('/chat/completions', async (req, res) => {
            try {
                const request = req.body;
                if (!request.provider || !request.model) {
                    return res.status(400).json({ error: 'Provider and model are required' });
                }
                const provider = this.providerManager.getProvider(request.provider);
                if (!provider) {
                    return res.status(400).json({ error: `Provider ${request.provider} not available` });
                }
                if (request.stream) {
                    // Handle streaming via SSE
                    res.writeHead(200, {
                        'Content-Type': 'text/event-stream',
                        'Cache-Control': 'no-cache',
                        'Connection': 'keep-alive',
                        'Access-Control-Allow-Origin': '*',
                        'Access-Control-Allow-Headers': 'Cache-Control'
                    });
                    try {
                        for await (const chunk of provider.chatStream(request)) {
                            res.write(`data: ${JSON.stringify(chunk)}\n\n`);
                            if (chunk.type === 'done' || chunk.type === 'error') {
                                break;
                            }
                        }
                    }
                    catch (error) {
                        res.write(`data: ${JSON.stringify({ type: 'error', error: error instanceof Error ? error.message : String(error) })}\n\n`);
                    }
                    res.write('data: [DONE]\n\n');
                    return res.end();
                }
                else {
                    // Handle non-streaming
                    const response = await provider.chat(request);
                    return res.json(response);
                }
            }
            catch (error) {
                console.error('Chat completion error:', error);
                return res.status(500).json({ error: error instanceof Error ? error.message : String(error) });
            }
        });
        // Providers info
        this.app.get('/providers', async (req, res) => {
            try {
                const providers = await this.providerManager.getProviderInfo();
                res.json(providers);
            }
            catch (error) {
                console.error('Error getting providers:', error);
                res.status(500).json({ error: 'Failed to get providers' });
            }
        });
        // Tools endpoint - returns available tools for the Rust client
        this.app.get('/tools', (req, res) => {
            // For now, return an empty array - tools will be managed by the Rust client
            // This endpoint is for future integration where the AI server might manage tools
            res.json({
                success: true,
                tools: []
            });
        });
        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json({ error: 'Endpoint not found' });
        });
    }
    setupWebSocket() {
        this.wss.on('connection', (ws) => {
            console.log('New WebSocket connection established');
            ws.on('message', async (data) => {
                try {
                    const message = JSON.parse(data.toString());
                    await this.handleWebSocketMessage(ws, message);
                }
                catch (error) {
                    console.error('WebSocket message error:', error);
                    ws.send(JSON.stringify({
                        type: 'error',
                        id: 'unknown',
                        data: { error: 'Invalid message format' }
                    }));
                }
            });
            ws.on('close', () => {
                console.log('WebSocket connection closed');
            });
            ws.on('error', (error) => {
                console.error('WebSocket error:', error);
            });
            // Send welcome message
            ws.send(JSON.stringify({
                type: 'connected',
                id: (0, uuid_1.v4)(),
                data: { message: 'Connected to Cara AI Server' }
            }));
        });
    }
    async handleWebSocketMessage(ws, message) {
        switch (message.type) {
            case 'models_request':
                try {
                    const models = await this.providerManager.getAllModels();
                    ws.send(JSON.stringify({
                        type: 'models_response',
                        id: message.id,
                        data: models
                    }));
                }
                catch (error) {
                    ws.send(JSON.stringify({
                        type: 'error',
                        id: message.id,
                        data: { error: error instanceof Error ? error.message : String(error) }
                    }));
                }
                break;
            case 'chat_request':
                await this.handleChatRequest(ws, message);
                break;
            default:
                ws.send(JSON.stringify({
                    type: 'error',
                    id: message.id,
                    data: { error: `Unknown message type: ${message.type}` }
                }));
        }
    }
    async handleChatRequest(ws, message) {
        try {
            const request = message.data;
            if (!request.provider || !request.model) {
                ws.send(JSON.stringify({
                    type: 'chat_error',
                    id: message.id,
                    data: { error: 'Provider and model are required' }
                }));
                return;
            }
            const provider = this.providerManager.getProvider(request.provider);
            if (!provider) {
                ws.send(JSON.stringify({
                    type: 'chat_error',
                    id: message.id,
                    data: { error: `Provider ${request.provider} not available` }
                }));
                return;
            }
            // Stream the response
            for await (const chunk of provider.chatStream(request)) {
                ws.send(JSON.stringify({
                    type: 'chat_chunk',
                    id: message.id,
                    data: chunk
                }));
                if (chunk.type === 'done' || chunk.type === 'error') {
                    break;
                }
            }
        }
        catch (error) {
            ws.send(JSON.stringify({
                type: 'chat_error',
                id: message.id,
                data: { error: error instanceof Error ? error.message : String(error) }
            }));
        }
    }
    start() {
        return new Promise((resolve) => {
            this.server.listen(this.port, () => {
                console.log(`🚀 Cara AI Server running on port ${this.port}`);
                console.log(`📊 Health check: http://localhost:${this.port}/health`);
                console.log(`🤖 Models endpoint: http://localhost:${this.port}/models`);
                console.log(`💬 WebSocket endpoint: ws://localhost:${this.port}`);
                console.log(`🔌 Available providers: ${this.providerManager.getAvailableProviders().join(', ')}`);
                resolve();
            });
        });
    }
    stop() {
        return new Promise((resolve) => {
            this.wss.close(() => {
                this.server.close(() => {
                    console.log('Cara AI Server stopped');
                    resolve();
                });
            });
        });
    }
}
exports.CaraAIServer = CaraAIServer;
//# sourceMappingURL=server.js.map