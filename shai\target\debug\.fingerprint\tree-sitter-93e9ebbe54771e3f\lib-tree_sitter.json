{"rustc": 1842507548689473721, "features": "[\"default\", \"std\"]", "declared_features": "[\"bindgen\", \"default\", \"std\", \"wasm\", \"wasmtime-c-api\"]", "target": 625726317299402132, "profile": 9735431057826086647, "path": 14092775650810111595, "deps": [[1882578318385763075, "build_script_build", false, 8683354685906231112], [6343042414849818538, "tree_sitter_language", false, 1016430780855174631], [9235208004366183979, "streaming_iterator", false, 16115850468300294764], [9408802513701742484, "regex_syntax", false, 13989830941985650695], [9451456094439810778, "regex", false, 15629433151217737311]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tree-sitter-93e9ebbe54771e3f\\dep-lib-tree_sitter", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}