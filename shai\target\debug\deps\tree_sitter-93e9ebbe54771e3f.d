D:\Sandeep\AXR\shai\target\debug\deps\tree_sitter-93e9ebbe54771e3f.d: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\../src/parser.h D:\Sandeep\AXR\shai\target\debug\build\tree-sitter-87e7edda4bb5e6ec\out/stdlib-symbols.txt

D:\Sandeep\AXR\shai\target\debug\deps\libtree_sitter-93e9ebbe54771e3f.rmeta: C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\lib.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\ffi.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\util.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./README.md C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./bindings.rs C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\../src/parser.h D:\Sandeep\AXR\shai\target\debug\build\tree-sitter-87e7edda4bb5e6ec\out/stdlib-symbols.txt

C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\lib.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\ffi.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\util.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./README.md:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\./bindings.rs:
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tree-sitter-0.24.7\binding_rust\../src/parser.h:
D:\Sandeep\AXR\shai\target\debug\build\tree-sitter-87e7edda4bb5e6ec\out/stdlib-symbols.txt:

# env-dep:OUT_DIR=D:\\Sandeep\\AXR\\shai\\target\\debug\\build\\tree-sitter-87e7edda4bb5e6ec\\out
