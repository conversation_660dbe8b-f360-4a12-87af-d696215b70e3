{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 1710777734951527303, "profile": 11851105015803045667, "path": 1479845197624743051, "deps": [[915123552320100963, "similar", false, 17135460932144530014], [1288403060204016458, "tokio_util", false, 754596817518635642], [1882578318385763075, "tree_sitter", false, 9679090628350134345], [2706460456408817945, "futures", false, 10569694034372510202], [4561882741388763810, "openai_dive", false, 15309105810466658345], [4934423331201470415, "fs", false, 4187465104665763321], [6227751574689719508, "termimad", false, 9706789758823393363], [6266526938907725535, "schemars", false, 13656464645612873766], [8218178811151724123, "reqwest", false, 16214819513314311909], [8319709847752024821, "uuid", false, 6288139715282346666], [8606274917505247608, "tracing", false, 4082791803751026407], [9451456094439810778, "regex", false, 15629433151217737311], [9538054652646069845, "tokio", false, 5432783451998911839], [9689903380558560274, "serde", false, 3174016326301431393], [9897246384292347999, "chrono", false, 3262687503025546763], [10806645703491011684, "thiserror", false, 2306002751661541288], [11594979262886006466, "tracing_appender", false, 10252266855207386944], [11946729385090170470, "async_trait", false, 16256111721926634546], [15367738274754116744, "serde_json", false, 10148534288263972318], [15426729956411809253, "cara_ai_client", false, 6461976363817957672], [15622660310229662834, "walkdir", false, 13066052315635845172], [16230660778393187092, "tracing_subscriber", false, 2275448246707015083], [16480958949081096160, "tree_sitter_highlight", false, 13889615000624561418], [16928111194414003569, "dirs", false, 5388579411683013009], [18360598505724663020, "cara_macros", false, 11372425923380477164]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cara-core-4540d6ae11ac454c\\dep-lib-cara_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}