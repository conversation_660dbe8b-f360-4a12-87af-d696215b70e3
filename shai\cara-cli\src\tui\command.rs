use std::{collections::HashMap, io, time::Duration};
use ratatui::widgets::Widget;

use crate::tui::App;

impl App<'_> {
    pub(crate) fn list_command() -> HashMap<(String, String),Vec<String>> {
        HashMap::from([
            (("/exit","exit from the tui"), vec![]),
            (("/auth","select a provider"), vec![]),
            (("/models","list all available models from all providers"), vec![]),
        ])
        .into_iter()
        .map(|((cmd,desc),args)|((cmd.to_string(),desc.to_string()),args.into_iter().map(|s: String|s.to_string()).collect()))
        .collect()
    }

    pub(crate) async fn handle_app_command(&mut self, command: &str) -> io::Result<()> {
        let mut parts = command.split_whitespace();
        let cmd = parts.next().unwrap();
        let args: Vec<&str> = parts.collect();

        match cmd {
            "/exit" => {
                self.exit = true;
            }
            "/models" => {
                self.input.alert_msg("Fetching available models...", Duration::from_secs(1));
                if let Err(e) = self.show_models().await {
                    self.input.alert_msg(&format!("Error fetching models: {}", e), Duration::from_secs(3));
                }
            }
            _ => {
                self.input.alert_msg("command unknown", Duration::from_secs(1));
            }
        }
        Ok(())
    }

    async fn show_models(&mut self) -> io::Result<()> {
        use cara_core::config::config::ShaiConfig;

        // Get AI client from config
        match ShaiConfig::get_ai_client().await {
            Ok((ai_client, _, _)) => {
                match ai_client.get_models().await {
                    Ok(models_response) => {
                        let mut display_text = String::new();
                        display_text.push_str("Available Models:\n\n");

                        for provider in &models_response.providers {
                            if provider.available && !provider.models.is_empty() {
                                display_text.push_str(&format!("🤖 {} ({} models)\n", provider.display_name, provider.models.len()));
                                for model in &provider.models {
                                    let context_info = if let Some(context) = model.context_length {
                                        format!(" ({}k context)", context / 1000)
                                    } else {
                                        String::new()
                                    };
                                    display_text.push_str(&format!("  • {}{}\n", model.name, context_info));
                                }
                                display_text.push('\n');
                            } else if !provider.available {
                                display_text.push_str(&format!("❌ {} (unavailable)\n", provider.display_name));
                                if let Some(error) = &provider.error {
                                    display_text.push_str(&format!("   Error: {}\n", error));
                                }
                                display_text.push('\n');
                            }
                        }

                        display_text.push_str(&format!("Total: {} models from {} providers",
                            models_response.total_models,
                            models_response.providers.len()));

                        // Display in terminal (this is a simplified version)
                        if let Some(ref mut terminal) = self.terminal {
                            let lines = display_text.lines().count() as u16;
                            terminal.insert_before(lines, |buf| {
                                use ratatui::text::Text;
                                use ansi_to_tui::IntoText;
                                let text = display_text.into_text().unwrap_or_else(|_| Text::raw(display_text.clone()));
                                text.render(buf.area, buf);
                            })?;
                        }

                        self.input.alert_msg("Models displayed above", Duration::from_secs(2));
                    }
                    Err(e) => {
                        self.input.alert_msg(&format!("Failed to fetch models: {}", e), Duration::from_secs(3));
                    }
                }
            }
            Err(e) => {
                self.input.alert_msg(&format!("Failed to connect to AI server: {}", e), Duration::from_secs(3));
            }
        }

        Ok(())
    }
}