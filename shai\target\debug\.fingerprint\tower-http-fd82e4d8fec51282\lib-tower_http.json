{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 2241668132362809309, "path": 16215392639322834235, "deps": [[784494742817713399, "tower_service", false, 8409841863435742618], [1906322745568073236, "pin_project_lite", false, 12041619693214720961], [4121350475192885151, "iri_string", false, 5435127861827111738], [5695049318159433696, "tower", false, 12051816234312005421], [7712452662827335977, "tower_layer", false, 1435022234505173063], [7896293946984509699, "bitflags", false, 3723376152487941458], [9010263965687315507, "http", false, 14254829711276313855], [10629569228670356391, "futures_util", false, 4810424942971930661], [14084095096285906100, "http_body", false, 18428748453503590490], [16066129441945555748, "bytes", false, 14053255451738189556]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-fd82e4d8fec51282\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}