"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeminiProvider = void 0;
const generative_ai_1 = require("@google/generative-ai");
const base_1 = require("./base");
class GeminiProvider extends base_1.BaseProvider {
    constructor(apiKey) {
        super(apiKey);
        this.name = 'gemini';
        this.displayName = 'Google Gemini';
        this.client = new generative_ai_1.GoogleGenerativeAI(this.apiKey);
    }
    async isAvailable() {
        try {
            const model = this.client.getGenerativeModel({ model: 'gemini-2.0-flash' });
            await model.generateContent('Hi');
            return true;
        }
        catch (error) {
            console.error('Gemini availability check failed:', error);
            return false;
        }
    }
    async getModels() {
        // Google doesn't provide a models API for Gemini, so we'll return known models
        return [
            {
                id: 'gemini-2.5-flash',
                name: 'Gemini 2.5 flash',
                provider: this.name,
                description: 'Most capable Gemini model with 2M token context',
                context_length: 2000000,
                supports_functions: true,
                supports_streaming: true
            },
            {
                id: 'gemini-2.0-flash-lite',
                name: 'Gemini 2.0 Flash',
                provider: this.name,
                description: 'Fast and efficient model with 1M token context',
                context_length: 1000000,
                supports_functions: true,
                supports_streaming: true
            },
            {
                id: 'gemini-2.0-flash',
                name: 'Gemini flash',
                provider: this.name,
                description: 'Balanced performance and capability',
                context_length: 32000,
                supports_functions: true,
                supports_streaming: true
            }
        ];
    }
    convertMessages(messages) {
        const history = [];
        let currentMessage = '';
        for (let i = 0; i < messages.length; i++) {
            const msg = messages[i];
            if (msg.role === 'system') {
                // System messages are handled differently in Gemini
                currentMessage = msg.content + '\n\n';
                continue;
            }
            if (i === messages.length - 1) {
                // Last message becomes the current message
                currentMessage += msg.content;
            }
            else {
                // Previous messages become history
                history.push({
                    role: msg.role === 'assistant' ? 'model' : 'user',
                    parts: [{ text: msg.content }]
                });
            }
        }
        return { history, message: currentMessage };
    }
    async chat(request) {
        this.validateRequest(request);
        try {
            const model = this.client.getGenerativeModel({
                model: request.model,
                generationConfig: {
                    temperature: request.temperature,
                    maxOutputTokens: request.max_tokens,
                    topP: request.top_p
                }
            });
            const { history, message } = this.convertMessages(request.messages);
            const chat = model.startChat({ history });
            const result = await chat.sendMessage(message);
            const response = await result.response;
            const id = this.generateId();
            return {
                id,
                object: 'chat.completion',
                created: this.getCurrentTimestamp(),
                model: request.model,
                choices: [{
                        index: 0,
                        message: {
                            role: 'assistant',
                            content: response.text()
                        },
                        finish_reason: 'stop'
                    }]
            };
        }
        catch (error) {
            throw await this.handleProviderError(error, 'chat');
        }
    }
    async *chatStream(request) {
        this.validateRequest(request);
        try {
            const model = this.client.getGenerativeModel({
                model: request.model,
                generationConfig: {
                    temperature: request.temperature,
                    maxOutputTokens: request.max_tokens,
                    topP: request.top_p
                }
            });
            const { history, message } = this.convertMessages(request.messages);
            const id = this.generateId();
            const chat = model.startChat({ history });
            const result = await chat.sendMessageStream(message);
            for await (const chunk of result.stream) {
                const chunkText = chunk.text();
                if (chunkText) {
                    yield this.createStreamChunk(chunkText, id);
                }
            }
            yield this.createDoneChunk(id);
        }
        catch (error) {
            yield this.createErrorChunk(`Gemini streaming error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
exports.GeminiProvider = GeminiProvider;
//# sourceMappingURL=gemini.js.map