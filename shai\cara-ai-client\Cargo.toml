[package]
name = "cara-ai-client"
version = "0.1.0"
edition = "2021"
description = "WebSocket client for communicating with Cara AI Server"
authors = ["Cara Team"]
license = "MIT"

[dependencies]
# WebSocket and HTTP client
tokio-tungstenite = { version = "0.21", features = ["native-tls"] }
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"
reqwest = { version = "0.12", features = ["json", "stream"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4"] }

# Error handling
thiserror = "2.0"
anyhow = "1.0"

# Async utilities
async-trait = "0.1"
async-stream = "0.3"
pin-project-lite = "0.2"

# Logging
tracing = "0.1"

# URL parsing
url = "2.5"

# Compatibility with existing shai-llm types
openai_dive = { version = "1.2", features = ["stream"] }
chrono = { version = "0.4", features = ["serde"] }

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.20.0"

[lints.rust]
dead_code = "allow"
unused_variables = "allow"
