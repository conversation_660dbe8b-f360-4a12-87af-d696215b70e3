"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OVHCloudProvider = exports.DeepSeekProvider = exports.GeminiProvider = exports.OpenAIProvider = exports.ProviderManager = void 0;
const openai_1 = require("./openai");
Object.defineProperty(exports, "OpenAIProvider", { enumerable: true, get: function () { return openai_1.OpenAIProvider; } });
// import { AnthropicProvider } from './anthropic';
const gemini_1 = require("./gemini");
Object.defineProperty(exports, "GeminiProvider", { enumerable: true, get: function () { return gemini_1.GeminiProvider; } });
const deepseek_1 = require("./deepseek");
Object.defineProperty(exports, "DeepSeekProvider", { enumerable: true, get: function () { return deepseek_1.DeepSeekProvider; } });
// import { MistralProvider } from './mistral';
const ovhcloud_1 = require("./ovhcloud");
Object.defineProperty(exports, "OVHCloudProvider", { enumerable: true, get: function () { return ovhcloud_1.OVHCloudProvider; } });
class ProviderManager {
    constructor() {
        this.providers = new Map();
        this.initializeProviders();
    }
    initializeProviders() {
        // Initialize providers based on available environment variables
        if (process.env.OPENAI_API_KEY) {
            this.providers.set('openai', new openai_1.OpenAIProvider(process.env.OPENAI_API_KEY));
        }
        // Anthropic provider temporarily disabled due to SDK compatibility issues
        // if (process.env.ANTHROPIC_API_KEY) {
        //   this.providers.set('anthropic', new AnthropicProvider(process.env.ANTHROPIC_API_KEY));
        // }
        if (process.env.GOOGLE_API_KEY) {
            this.providers.set('gemini', new gemini_1.GeminiProvider(process.env.GOOGLE_API_KEY));
        }
        if (process.env.DEEPSEEK_API_KEY) {
            this.providers.set('deepseek', new deepseek_1.DeepSeekProvider(process.env.DEEPSEEK_API_KEY, process.env.DEEPSEEK_BASE_URL));
        }
        // Mistral provider temporarily disabled due to SDK compatibility issues
        // if (process.env.MISTRAL_API_KEY) {
        //   this.providers.set('mistral', new MistralProvider(process.env.MISTRAL_API_KEY));
        // }
        // OVH Cloud - can work without API key in anonymous mode
        this.providers.set('ovhcloud', new ovhcloud_1.OVHCloudProvider(process.env.OVH_API_KEY, process.env.OVH_BASE_URL));
        console.log(`Initialized ${this.providers.size} providers:`, Array.from(this.providers.keys()));
    }
    getProvider(name) {
        return this.providers.get(name);
    }
    getAllProviders() {
        return Array.from(this.providers.values());
    }
    getAvailableProviders() {
        return Array.from(this.providers.keys());
    }
    async getProviderInfo() {
        const providerInfos = [];
        for (const [name, provider] of this.providers) {
            try {
                const available = await provider.isAvailable();
                const models = available ? await provider.getModels() : [];
                providerInfos.push({
                    name,
                    display_name: provider.displayName,
                    models,
                    available
                });
            }
            catch (error) {
                providerInfos.push({
                    name,
                    display_name: provider.displayName,
                    models: [],
                    available: false,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        return providerInfos.sort((a, b) => a.display_name.localeCompare(b.display_name));
    }
    async getAllModels() {
        const providers = await this.getProviderInfo();
        const total_models = providers.reduce((sum, provider) => sum + provider.models.length, 0);
        return {
            providers,
            total_models
        };
    }
    async findModel(modelId) {
        for (const provider of this.providers.values()) {
            try {
                const models = await provider.getModels();
                const model = models.find(m => m.id === modelId);
                if (model) {
                    return { provider, model };
                }
            }
            catch (error) {
                console.error(`Error getting models from ${provider.name}:`, error);
            }
        }
        return null;
    }
    hasProvider(name) {
        return this.providers.has(name);
    }
    getProviderCount() {
        return this.providers.size;
    }
}
exports.ProviderManager = ProviderManager;
// Export types
__exportStar(require("../types"), exports);
//# sourceMappingURL=index.js.map