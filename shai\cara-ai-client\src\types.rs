use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Re-export types from openai_dive for compatibility
pub use openai_dive::v1::resources::{
    chat::{ChatCompletionParameters, ChatCompletionResponse, ChatMessage, ChatMessageContent, ChatCompletionChunkResponse},
    model::ListModelResponse,
};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatRequest {
    pub provider: String,
    pub model: String,
    pub messages: Vec<ChatMessage>,
    pub stream: Option<bool>,
    pub temperature: Option<f32>,
    pub max_tokens: Option<u32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub stop: Option<Vec<String>>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct StreamChunk {
    pub r#type: String, // "chunk", "error", "done"
    pub id: Option<String>,
    pub content: Option<String>,
    pub error: Option<String>,
    pub finish_reason: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelInfo {
    pub id: String,
    pub name: String,
    pub provider: String,
    pub description: Option<String>,
    pub context_length: Option<u32>,
    pub supports_functions: Option<bool>,
    pub supports_streaming: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderInfo {
    pub name: String,
    pub display_name: String,
    pub models: Vec<ModelInfo>,
    pub available: bool,
    pub error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelsResponse {
    pub providers: Vec<ProviderInfo>,
    pub total_models: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthResponse {
    pub status: String,
    pub version: String,
    pub uptime: u64,
    pub providers: HashMap<String, ProviderStatus>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProviderStatus {
    pub available: bool,
    pub error: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketMessage {
    pub r#type: String,
    pub id: String,
    pub data: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionConfig {
    pub server_url: String,
    pub timeout_ms: u64,
    pub reconnect_attempts: u32,
    pub reconnect_delay_ms: u64,
}

impl Default for ConnectionConfig {
    fn default() -> Self {
        Self {
            server_url: "ws://localhost:3001".to_string(),
            timeout_ms: 30000,
            reconnect_attempts: 3,
            reconnect_delay_ms: 1000,
        }
    }
}

// Convert from ChatCompletionParameters to ChatRequest
impl From<ChatCompletionParameters> for ChatRequest {
    fn from(params: ChatCompletionParameters) -> Self {
        Self {
            provider: "openai".to_string(), // Default provider, should be set by caller
            model: params.model,
            messages: params.messages,
            stream: params.stream,
            temperature: params.temperature,
            max_tokens: params.max_tokens,
            top_p: params.top_p,
            frequency_penalty: params.frequency_penalty,
            presence_penalty: params.presence_penalty,
            stop: None, // Simplified for now
        }
    }
}

// Convert StreamChunk to ChatCompletionChunkResponse for compatibility
impl From<StreamChunk> for ChatCompletionChunkResponse {
    fn from(chunk: StreamChunk) -> Self {
        use openai_dive::v1::resources::chat::{ChatCompletionChunkChoice, DeltaChatMessage};
        use openai_dive::v1::resources::shared::FinishReason;

        let finish_reason = None; // Simplified for now

        let choice = ChatCompletionChunkChoice {
            index: Some(0),
            delta: if let Some(content) = chunk.content {
                DeltaChatMessage::Assistant {
                    content: Some(ChatMessageContent::Text(content)),
                    name: None,
                    tool_calls: None,
                    reasoning_content: None,
                    refusal: None,
                }
            } else {
                DeltaChatMessage::Assistant {
                    content: None,
                    name: None,
                    tool_calls: None,
                    reasoning_content: None,
                    refusal: None,
                }
            },
            finish_reason: finish_reason.clone(),
            logprobs: None,
        };

        Self {
            id: Some(chunk.id.unwrap_or_else(|| uuid::Uuid::new_v4().to_string())),
            object: "chat.completion.chunk".to_string(),
            created: chrono::Utc::now().timestamp() as u32,
            model: "unknown".to_string(),
            choices: vec![choice],
            system_fingerprint: None,
            usage: None,
        }
    }
}
