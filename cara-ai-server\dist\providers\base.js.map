{"version": 3, "file": "base.js", "sourceRoot": "", "sources": ["../../src/providers/base.ts"], "names": [], "mappings": ";;;AAEA,MAAsB,YAAY;IAOhC,YAAY,MAAe,EAAE,OAAgB;QAC3C,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAOS,eAAe,CAAC,OAAoB;QAC5C,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,0BAA0B;QAC1B,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACvC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7E,MAAM,IAAI,KAAK,CAAC,yBAAyB,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;YAC3D,CAAC;YACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;IACH,CAAC;IAES,iBAAiB,CAAC,OAAe,EAAE,EAAW,EAAE,aAA6B;QACrF,OAAO;YACL,IAAI,EAAE,OAAO;YACb,EAAE;YACF,OAAO;YACP,aAAa;SACd,CAAC;IACJ,CAAC;IAES,gBAAgB,CAAC,KAAa;QACtC,OAAO;YACL,IAAI,EAAE,OAAO;YACb,KAAK;SACN,CAAC;IACJ,CAAC;IAES,eAAe,CAAC,EAAW;QACnC,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,EAAE;SACH,CAAC;IACJ,CAAC;IAES,UAAU;QAClB,OAAO,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC7E,CAAC;IAES,mBAAmB;QAC3B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACvC,CAAC;IAES,KAAK,CAAC,mBAAmB,CAAC,KAAU,EAAE,OAAe;QAC7D,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,sBAAsB,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;QAEnE,IAAI,OAAO,GAAG,GAAG,IAAI,CAAC,WAAW,UAAU,CAAC;QAE5C,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;YACzC,OAAO,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;QAC/C,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;QAC3B,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,wBAAwB,CAAC;QACtC,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;CACF;AAnFD,oCAmFC"}