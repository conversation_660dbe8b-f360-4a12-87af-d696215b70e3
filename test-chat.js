const http = require('http');

const testMessage = {
  provider: 'ovhcloud',
  model: 'Qwen3-32B',
  messages: [
    {
      role: 'user',
      content: 'Hello! Can you tell me what you are in one sentence?'
    }
  ],
  stream: false
};

const postData = JSON.stringify(testMessage);

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/chat/completions',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData)
  }
};

console.log('Testing chat completion with OVH Cloud...');

const req = http.request(options, (res) => {
  console.log(`Status: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      console.log('Chat Response:', JSON.stringify(response, null, 2));
    } catch (e) {
      console.log('Raw Response:', data);
    }
  });
});

req.on('error', (err) => {
  console.error('Error:', err.message);
});

req.write(postData);
req.end();
