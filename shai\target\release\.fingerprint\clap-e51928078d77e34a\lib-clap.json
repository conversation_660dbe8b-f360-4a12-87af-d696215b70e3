{"rustc": 1842507548689473721, "features": "[\"color\", \"default\", \"derive\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 9656904095642909417, "path": 1405726192882911974, "deps": [[4925398738524877221, "clap_derive", false, 4352065082959589379], [14814905555676593471, "clap_builder", false, 12103035672957109651]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\clap-e51928078d77e34a\\dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}