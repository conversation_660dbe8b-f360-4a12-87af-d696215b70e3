{"rustc": 1842507548689473721, "features": "[\"color\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-doc\", \"unstable-ext\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 6917651628887788201, "profile": 15599109589607159429, "path": 8101555014842407130, "deps": [[5820056977320921005, "anstream", false, 10260361340131679565], [9394696648929125047, "anstyle", false, 17760464395744199548], [11166530783118767604, "strsim", false, 97629380629919987], [11649982696571033535, "clap_lex", false, 4387260955843887866]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\clap_builder-9bd8a463335d48a1\\dep-lib-clap_builder", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}