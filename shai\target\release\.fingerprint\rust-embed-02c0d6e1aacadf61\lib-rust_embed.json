{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"actix\", \"actix-web\", \"axum\", \"axum-ex\", \"compression\", \"debug-embed\", \"deterministic-timestamps\", \"hex\", \"include-exclude\", \"include-flate\", \"interpolate-folder-path\", \"mime-guess\", \"mime_guess\", \"poem\", \"poem-ex\", \"rocket\", \"salvo\", \"salvo-ex\", \"tokio\", \"warp\", \"warp-ex\"]", "target": 3385222681681722461, "profile": 2040997289075261528, "path": 13381408126050167754, "deps": [[5409933923103361951, "rust_embed_utils", false, 41779225142019415], [11693977163544003021, "rust_embed_impl", false, 14614257486710295496], [15622660310229662834, "walkdir", false, 8487875634976311419]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rust-embed-02c0d6e1aacadf61\\dep-lib-rust_embed", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}