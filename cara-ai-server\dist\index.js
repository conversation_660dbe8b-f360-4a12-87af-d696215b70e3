"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const server_1 = require("./server");
// Load environment variables
dotenv_1.default.config();
// Validate required environment variables
const requiredEnvVars = [];
const availableProviders = [];
if (process.env.OPENAI_API_KEY) {
    availableProviders.push('OpenAI');
}
if (process.env.ANTHROPIC_API_KEY) {
    availableProviders.push('Anthropic');
}
if (process.env.GOOGLE_API_KEY) {
    availableProviders.push('Google Gemini');
}
if (process.env.DEEPSEEK_API_KEY) {
    availableProviders.push('DeepSeek');
}
if (process.env.MISTRAL_API_KEY) {
    availableProviders.push('Mistral');
}
if (availableProviders.length === 0) {
    console.warn('⚠️  No AI provider API keys found in environment variables.');
    console.warn('   Please set at least one of the following:');
    console.warn('   - OPENAI_API_KEY');
    console.warn('   - ANTHROPIC_API_KEY');
    console.warn('   - GOOGLE_API_KEY');
    console.warn('   - DEEPSEEK_API_KEY');
    console.warn('   - MISTRAL_API_KEY');
    console.warn('');
    console.warn('   Copy .env.example to .env and add your API keys.');
}
else {
    console.log(`✅ Found API keys for: ${availableProviders.join(', ')}`);
}
const port = parseInt(process.env.PORT || '3001', 10);
const server = new server_1.CaraAIServer(port);
// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    await server.stop();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    await server.stop();
    process.exit(0);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// Start the server
async function main() {
    try {
        await server.start();
    }
    catch (error) {
        console.error('❌ Failed to start server:', error);
        process.exit(1);
    }
}
main();
//# sourceMappingURL=index.js.map