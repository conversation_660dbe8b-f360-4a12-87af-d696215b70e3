"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAIProvider = void 0;
const openai_1 = __importDefault(require("openai"));
const base_1 = require("./base");
class OpenAIProvider extends base_1.BaseProvider {
    constructor(apiKey) {
        super(apiKey);
        this.name = 'openai';
        this.displayName = 'OpenAI';
        this.client = new openai_1.default({
            apiKey: this.apiKey
        });
    }
    async isAvailable() {
        try {
            await this.client.models.list();
            return true;
        }
        catch (error) {
            console.error('OpenAI availability check failed:', error);
            return false;
        }
    }
    async getModels() {
        try {
            const response = await this.client.models.list();
            return response.data
                .filter(model => model.id.includes('gpt'))
                .map(model => ({
                id: model.id,
                name: model.id,
                provider: this.name,
                description: `OpenAI ${model.id}`,
                supports_functions: model.id.includes('gpt-4') || model.id.includes('gpt-3.5'),
                supports_streaming: true
            }))
                .sort((a, b) => {
                // Prioritize GPT-4 models
                if (a.id.includes('gpt-4') && !b.id.includes('gpt-4'))
                    return -1;
                if (!a.id.includes('gpt-4') && b.id.includes('gpt-4'))
                    return 1;
                return a.id.localeCompare(b.id);
            });
        }
        catch (error) {
            throw await this.handleProviderError(error, 'getModels');
        }
    }
    async chat(request) {
        this.validateRequest(request);
        try {
            const response = await this.client.chat.completions.create({
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.max_tokens,
                top_p: request.top_p,
                frequency_penalty: request.frequency_penalty,
                presence_penalty: request.presence_penalty,
                stop: request.stop,
                stream: false
            });
            return {
                id: response.id,
                object: response.object,
                created: response.created,
                model: response.model,
                choices: response.choices.map(choice => ({
                    index: choice.index,
                    message: {
                        role: choice.message.role,
                        content: choice.message.content || ''
                    },
                    finish_reason: choice.finish_reason
                })),
                usage: response.usage ? {
                    prompt_tokens: response.usage.prompt_tokens,
                    completion_tokens: response.usage.completion_tokens,
                    total_tokens: response.usage.total_tokens
                } : undefined
            };
        }
        catch (error) {
            throw await this.handleProviderError(error, 'chat');
        }
    }
    async *chatStream(request) {
        this.validateRequest(request);
        try {
            const stream = await this.client.chat.completions.create({
                model: request.model,
                messages: request.messages,
                temperature: request.temperature,
                max_tokens: request.max_tokens,
                top_p: request.top_p,
                frequency_penalty: request.frequency_penalty,
                presence_penalty: request.presence_penalty,
                stop: request.stop,
                stream: true
            });
            for await (const chunk of stream) {
                const choice = chunk.choices[0];
                if (choice?.delta?.content) {
                    yield this.createStreamChunk(choice.delta.content, chunk.id, choice.finish_reason);
                }
                if (choice?.finish_reason) {
                    yield this.createDoneChunk(chunk.id);
                    break;
                }
            }
        }
        catch (error) {
            yield this.createErrorChunk(`OpenAI streaming error: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
exports.OpenAIProvider = OpenAIProvider;
//# sourceMappingURL=openai.js.map