{"$message_type":"diagnostic","message":"unused import: `std::sync::Arc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":4,"byte_end":18,"line_start":1,"line_end":1,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":0,"byte_end":21,"line_start":1,"line_end":2,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::sync::Arc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:1:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::sync::Arc;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `TimeDelta` and `Utc`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":36,"byte_end":45,"line_start":3,"line_end":3,"column_start":14,"column_end":23,"is_primary":true,"text":[{"text":"use chrono::{TimeDelta, Utc};","highlight_start":14,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":47,"byte_end":50,"line_start":3,"line_end":3,"column_start":25,"column_end":28,"is_primary":true,"text":[{"text":"use chrono::{TimeDelta, Utc};","highlight_start":25,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":23,"byte_end":54,"line_start":3,"line_end":4,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use chrono::{TimeDelta, Utc};","highlight_start":1,"highlight_end":30},{"text":"use cara_ai_client::ChatMessage;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `TimeDelta` and `Utc`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:3:14\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chrono::{TimeDelta, Utc};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `cara_ai_client::ChatMessage`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":58,"byte_end":85,"line_start":4,"line_end":4,"column_start":5,"column_end":32,"is_primary":true,"text":[{"text":"use cara_ai_client::ChatMessage;","highlight_start":5,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":54,"byte_end":88,"line_start":4,"line_end":5,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use cara_ai_client::ChatMessage;","highlight_start":1,"highlight_end":33},{"text":"// Note: ToolCall functionality will be implemented later","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `cara_ai_client::ChatMessage`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:4:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse cara_ai_client::ChatMessage;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `RwLock` and `broadcast`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":165,"byte_end":174,"line_start":6,"line_end":6,"column_start":19,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::sync::{broadcast, RwLock};","highlight_start":19,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":176,"byte_end":182,"line_start":6,"line_end":6,"column_start":30,"column_end":36,"is_primary":true,"text":[{"text":"use tokio::sync::{broadcast, RwLock};","highlight_start":30,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":147,"byte_end":186,"line_start":6,"line_end":7,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::sync::{broadcast, RwLock};","highlight_start":1,"highlight_end":38},{"text":"use tokio::task::JoinHandle;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `RwLock` and `broadcast`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:6:19\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::sync::{broadcast, RwLock};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::task::JoinHandle`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":190,"byte_end":213,"line_start":7,"line_end":7,"column_start":5,"column_end":28,"is_primary":true,"text":[{"text":"use tokio::task::JoinHandle;","highlight_start":5,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":186,"byte_end":216,"line_start":7,"line_end":8,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::task::JoinHandle;","highlight_start":1,"highlight_end":29},{"text":"use tokio_util::sync::CancellationToken;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tokio::task::JoinHandle`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::task::JoinHandle;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio_util::sync::CancellationToken`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":220,"byte_end":255,"line_start":8,"line_end":8,"column_start":5,"column_end":40,"is_primary":true,"text":[{"text":"use tokio_util::sync::CancellationToken;","highlight_start":5,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":216,"byte_end":258,"line_start":8,"line_end":9,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio_util::sync::CancellationToken;","highlight_start":1,"highlight_end":41},{"text":"use tracing::info;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tokio_util::sync::CancellationToken`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:8:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio_util::sync::CancellationToken;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tracing::info`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":262,"byte_end":275,"line_start":9,"line_end":9,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"use tracing::info;","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":258,"byte_end":278,"line_start":9,"line_end":10,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::info;","highlight_start":1,"highlight_end":19},{"text":"use serde_json::from_str;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tracing::info`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:9:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::info;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `serde_json::from_str`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":282,"byte_end":302,"line_start":10,"line_end":10,"column_start":5,"column_end":25,"is_primary":true,"text":[{"text":"use serde_json::from_str;","highlight_start":5,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":278,"byte_end":305,"line_start":10,"line_end":11,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use serde_json::from_str;","highlight_start":1,"highlight_end":26},{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `serde_json::from_str`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:10:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m10\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde_json::from_str;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `uuid::Uuid`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":309,"byte_end":319,"line_start":11,"line_end":11,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":305,"byte_end":322,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use uuid::Uuid;","highlight_start":1,"highlight_end":16},{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `uuid::Uuid`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse uuid::Uuid;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AgentEvent`, `ClaimManager`, `InternalAgentEvent`, `InternalAgentState`, `PermissionRequest`, and `PermissionResponse`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":352,"byte_end":362,"line_start":12,"line_end":12,"column_start":31,"column_end":41,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":31,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":364,"byte_end":376,"line_start":12,"line_end":12,"column_start":43,"column_end":55,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":43,"highlight_end":55}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":378,"byte_end":396,"line_start":12,"line_end":12,"column_start":57,"column_end":75,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":57,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":398,"byte_end":416,"line_start":12,"line_end":12,"column_start":77,"column_end":95,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":77,"highlight_end":95}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":418,"byte_end":435,"line_start":12,"line_end":12,"column_start":97,"column_end":114,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":97,"highlight_end":114}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":437,"byte_end":455,"line_start":12,"line_end":12,"column_start":116,"column_end":134,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":116,"highlight_end":134}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":350,"byte_end":455,"line_start":12,"line_end":12,"column_start":29,"column_end":134,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":29,"highlight_end":134}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":340,"byte_end":341,"line_start":12,"line_end":12,"column_start":19,"column_end":20,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":19,"highlight_end":20}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":455,"byte_end":456,"line_start":12,"line_end":12,"column_start":134,"column_end":135,"is_primary":true,"text":[{"text":"use crate::agent::{AgentCore, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};","highlight_start":134,"highlight_end":135}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `AgentEvent`, `ClaimManager`, `InternalAgentEvent`, `InternalAgentState`, `PermissionRequest`, and `PermissionResponse`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:12:31\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0me, AgentEvent, ClaimManager, InternalAgentEvent, InternalAgentState, PermissionRequest, PermissionResponse};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `AnyTool`, `ToolCall`, `ToolCapability`, and `ToolResult`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":478,"byte_end":485,"line_start":13,"line_end":13,"column_start":20,"column_end":27,"is_primary":true,"text":[{"text":"use crate::tools::{AnyTool, ToolCall, ToolCapability, ToolResult};","highlight_start":20,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":487,"byte_end":495,"line_start":13,"line_end":13,"column_start":29,"column_end":37,"is_primary":true,"text":[{"text":"use crate::tools::{AnyTool, ToolCall, ToolCapability, ToolResult};","highlight_start":29,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":497,"byte_end":511,"line_start":13,"line_end":13,"column_start":39,"column_end":53,"is_primary":true,"text":[{"text":"use crate::tools::{AnyTool, ToolCall, ToolCapability, ToolResult};","highlight_start":39,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":513,"byte_end":523,"line_start":13,"line_end":13,"column_start":55,"column_end":65,"is_primary":true,"text":[{"text":"use crate::tools::{AnyTool, ToolCall, ToolCapability, ToolResult};","highlight_start":55,"highlight_end":65}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":459,"byte_end":527,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use crate::tools::{AnyTool, ToolCall, ToolCapability, ToolResult};","highlight_start":1,"highlight_end":67},{"text":"use tracing::debug;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `AnyTool`, `ToolCall`, `ToolCapability`, and `ToolResult`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:13:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::tools::{AnyTool, ToolCall, ToolCapability, ToolResult};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tracing::debug`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":531,"byte_end":545,"line_start":14,"line_end":14,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"use tracing::debug;","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"cara-core\\src\\agent\\actions\\tools.rs","byte_start":527,"byte_end":548,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tracing::debug;","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `tracing::debug`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mcara-core\\src\\agent\\actions\\tools.rs:14:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::debug;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"12 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 12 warnings emitted\u001b[0m\n\n"}
