use std::path::PathBuf;
use std::fs;
#[cfg(unix)]
use std::os::unix::fs::PermissionsExt;
use reqwest::Url;
use serde::{Serialize, Deserialize};
use cara_ai_client::CaraAIClient;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ProviderConfig {
    pub provider: String,
    pub model: String,
    pub enabled: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ShaiConfig {
    pub providers: Vec<ProviderConfig>,
    pub selected_provider: usize,
    pub ai_server_url: String,
}

impl ShaiConfig {
    pub async fn pull_from_url(url: Url) -> Result<Self, Box<dyn std::error::Error>> {
        let response = reqwest::get(url).await?;
        let config_json = response.text().await?;
        let config: ShaiConfig = serde_json::from_str(&config_json)?;
        Ok(config)
    }

    pub fn add_provider(&mut self, provider: String, model: String) -> usize {
        let provider_config = ProviderConfig {
            provider,
            model,
            enabled: true,
        };

        self.providers.push(provider_config);
        self.providers.len() - 1
    }

    pub fn is_duplicate_config(&self, provider_name: &str, model: &str) -> bool {
        self.providers.iter().any(|provider_config| {
            provider_config.provider == provider_name &&
            provider_config.model.eq(model)
        })
    }

    pub fn get_selected_provider(&self) -> Option<&ProviderConfig> {
        self.providers.get(self.selected_provider)
    }

    pub fn get_selected_provider_mut(&mut self) -> Option<&mut ProviderConfig> {
        self.providers.get_mut(self.selected_provider)
    }

    pub fn set_selected_provider(&mut self, index: usize) -> Result<(), String> {
        if index < self.providers.len() {
            self.selected_provider = index;
            Ok(())
        } else {
            Err(format!("Provider index {} out of bounds (have {} providers)", index, self.providers.len()))
        }
    }

    pub fn config_path() -> Result<PathBuf, Box<dyn std::error::Error>> {
        let config_dir = std::env::var("XDG_CONFIG_HOME")
            .map(PathBuf::from)
            .or_else(|_| {
                dirs::home_dir()
                    .map(|home| home.join(".config"))
                    .ok_or("Could not find home directory")
            })?;
        
        let cara_config_dir = config_dir.join("cara");
        std::fs::create_dir_all(&cara_config_dir)?;

        Ok(cara_config_dir.join("auth.config"))
    }

    pub fn load() -> Result<ShaiConfig, Box<dyn std::error::Error>> {
        let config_path = Self::config_path()?;
        
        if !config_path.exists() {
            return Err("config file does not exist".into());
        }

        let content = fs::read_to_string(config_path)?;
        let mut config: ShaiConfig = serde_json::from_str(&content)?;
        
        // Validate selected_provider index
        if config.providers.is_empty() {
            config.selected_provider = 0;
        } else if config.selected_provider >= config.providers.len() {
            config.selected_provider = 0; // Reset to first provider if index is invalid
        }
        
        Ok(config)
    }

    pub fn save(&self) -> Result<(), Box<dyn std::error::Error>> {
        let config_path = Self::config_path()?;
        let content = serde_json::to_string_pretty(self)?;
        fs::write(&config_path, content)?;
        
        // Set file permissions to 600 (user read/write only) on Unix systems
        #[cfg(unix)]
        {
            let mut perms = fs::metadata(&config_path)?.permissions();
            perms.set_mode(0o600);
            fs::set_permissions(&config_path, perms)?;
        }
        
        Ok(())
    }

    pub fn exists() -> bool {
        Self::config_path()
            .map(|path| path.exists())
            .unwrap_or(false)
    }



    pub fn remove_provider(&mut self, index: usize) -> Result<ProviderConfig, String> {
        if index >= self.providers.len() {
            return Err(format!("Provider index {} out of bounds (have {} providers)", index, self.providers.len()));
        }

        if self.providers.len() == 1 {
            return Err("Cannot remove the last provider".to_string());
        }

        let removed = self.providers.remove(index);

        // Adjust selected_provider if needed
        if self.selected_provider >= self.providers.len() {
            self.selected_provider = self.providers.len() - 1;
        } else if self.selected_provider > index {
            self.selected_provider -= 1;
        }

        Ok(removed)
    }

    pub fn list_providers(&self) -> Vec<(usize, &str, &str)> {
        self.providers
            .iter()
            .enumerate()
            .map(|(i, config)| (i, config.provider.as_str(), config.model.as_str()))
            .collect()
    }

    pub fn find_providers_by_type(&self, provider_type: &str) -> Vec<usize> {
        self.providers
            .iter()
            .enumerate()
            .filter_map(|(i, config)| {
                if config.provider == provider_type {
                    Some(i)
                } else {
                    None
                }
            })
            .collect()
    }
}

impl Default for ShaiConfig {
    fn default() -> Self {
        Self {
            // Default providers - will be populated from AI server
            providers: vec![
                ProviderConfig {
                    provider: "openai".to_string(),
                    model: "gpt-4".to_string(),
                    enabled: true,
                },
                ProviderConfig {
                    provider: "anthropic".to_string(),
                    model: "claude-3-5-sonnet-20241022".to_string(),
                    enabled: true,
                },
            ],
            selected_provider: 0,
            ai_server_url: "ws://localhost:3001".to_string(),
        }
    }
}

impl ShaiConfig {
    pub async fn get_ai_client() -> Result<(CaraAIClient, String, String), Box<dyn std::error::Error>>{
        let config = ShaiConfig::load()
            .unwrap_or_else(|_| ShaiConfig::default());

        let ai_client = CaraAIClient::with_server_url(config.ai_server_url.clone());

        // Test connection to AI server
        ai_client.test_connection().await
            .map_err(|e| format!("Failed to connect to AI server at {}: {}", config.ai_server_url, e))?;

        if let Some(provider_config) = config.get_selected_provider() {
            // Skip provider availability check for now - assume providers are available
            // This allows the agent to start without requiring complex provider validation
            // TODO: Re-enable provider availability check once the /providers endpoint is stable

            Ok((ai_client, provider_config.provider.clone(), provider_config.model.clone()))
        } else {
            Err("No provider configured".into())
        }
    }
}